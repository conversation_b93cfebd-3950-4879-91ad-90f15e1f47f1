name: <PERSON><PERSON> NonMain

on:
  push:
    paths:
      - '**/src/**'
      - 'build.gradle'
      - 'gradle.properties'

jobs:
  build:
    uses: cvs-health-source-code/gha_workflow_actions/.github/workflows/gradle_docker.yaml@latest
    secrets: inherit
    with:
      JAVA_VERSION: "21.0.3"
      HARNESS_DEPLOY_FILE: "NA"
      SKIP_SEMANTIC: true
      JAVA_LINT_COMMAND: false
      LIBRARY_PUBLISH: false
      SECURITY_GATE_TEAM_NAME: oms-unification

#
#name: CI Build Pipeline
#
#on:
#  push:
#    branches:
#      - storeEdge*
#    paths-ignore:
#      - 'deploy-configs/**'
#      - 'README.md'
#      - '.github/workflows/cd.yaml'
#
#jobs:
#  cicd:
#    uses: cvs-health-source-code/gha_workflow_actions/.github/workflows/gradle_docker.yaml@semantic_release_fix
#    secrets: inherit
#    with:
#      JAVA_VERSION: "21.0.3"
#      JAVA_LINT_COMMAND: "echo \"Lint\""
#      SKIP_SEMANTIC: true