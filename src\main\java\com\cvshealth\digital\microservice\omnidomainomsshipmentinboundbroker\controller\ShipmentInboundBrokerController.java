package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.controller;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.exception.model.ApiStatusCodes;
import com.cvshealth.digital.framework.starter.exception.model.FaultTypes;
import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.ShipmentUpdateEventResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.PnpShipmentUpdateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * The ShipmentInboundBrokerController class.
 *
 * <AUTHOR> Brizard
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("${service.context-path}")
public class ShipmentInboundBrokerController {

    private final EventPublisherService eventPublisherService;
    private final ObjectMapper objectMapper;

    @Tag(name = "updatePNPShipmentStatus")
    @Operation(summary = "handles PNP shipment update event request", description = "handle a PNP shipment update event request")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Success Response",      content = { @Content(mediaType = "application/json", schema = @Schema (implementation = ShipmentUpdateEventResponse.class))}),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Bad Request",           content = { @Content(mediaType = "application/json", schema = @Schema (implementation = ApiResponse.class))}),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "422", description = "Error processing the request", content = { @Content(mediaType = "application/json", schema = @Schema (implementation = ApiResponse.class))}),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal Server Error", content = { @Content(mediaType = "application/json", schema = @Schema (implementation = ApiResponse.class))})
    })
    @PostMapping(value = "/update-shipment-status/pnp", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse updatePNPShipmentStatus(@RequestBody PnpShipmentUpdateRequest request) throws ApiException {

        try {
            // put the received message on the inbound broker topic with "sourceSystemType" header as "PNP"
            String requestString = objectMapper.writeValueAsString(request);
            return eventPublisherService.publishEvent("shipmentUpdateEvent", requestString, Map.of("sourceSystemType", "PNP"));
        } catch (Exception e) {
            throw new ApiException(ApiStatusCodes.INTERNAL_SERVER_ERROR, FaultTypes.ERROR);
        }
    }

}
