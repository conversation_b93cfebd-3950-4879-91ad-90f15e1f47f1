<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <!-- ####################################### -->
    <!-- Console appender -->
    <!-- ####################################### -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{trackingID}] %logger{36}:%line - %msg%n%rEx{full, org, sun, java.lang.reflect}
            </Pattern>
        </encoder>
    </appender>

    <appender name="EVENTLOGGER" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                %msg%n
            </Pattern>
        </encoder>
    </appender>

    <!-- ####################################### -->
    <!-- Loggers  - Common for all env -->
    <!-- ####################################### -->

    <!-- TEST -->
    <springProfile name="test">

        <logger name="com.cvshealth" level="debug" additivity="false">
            <appender-ref ref="STDOUT" />
        </logger>

        <!-- REST low level request and response logs at trace level -->
        <!--
        <logger name="com.cvshealth.digital.framework.service.rest" level="trace" additivity="false">
            <appender-ref ref="STDOUT" />
        </logger>
        -->

        <!-- CVSEVENT logs at info level -->
        <logger name="CvsEventLogger" level="info" additivity="false">
            <appender-ref ref="EVENTLOGGER" />
        </logger>

        <root level="info">
            <appender-ref ref="STDOUT" />
        </root>

    </springProfile>


</configuration>