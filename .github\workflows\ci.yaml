name: CI Main

on:
  push:
    branches:
      - main
    paths:
      - '**/src/**'
      - 'build.gradle'
      - 'gradle.properties'

jobs:
  build:
    uses: cvs-health-source-code/gha_workflow_actions/.github/workflows/gradle_docker.yaml@latest
    secrets: inherit
    with:
      JAVA_VERSION: "21.0.3"
      HARNESS_DEPLOY_FILE: "NA"
      SKIP_SEMANTIC: false
      JAVA_LINT_COMMAND: false
      LIBRARY_PUBLISH: false
      SECURITY_GATE_TEAM_NAME: oms-unification