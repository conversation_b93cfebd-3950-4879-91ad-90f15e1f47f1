# namespace, app version and image details
appData:
  nameSpace: oms-qa
  dockerHub: cvsh.jfrog.io
  dockerImageGroupName: cvsdigital-docker/ghemu/cvs-health-source-code/omni-domain-oms-shipment-inbound-broker
  dockerImageName: omni-domain-oms-shipment-inbound-broker
  dockerImageTag: 1.0.26

# hpa settings - min and max replicas and minAvailable for PDB
hpaSettings:
  minAvailable: 1
  minReplicas: 1
  maxReplicas: 1
  averageUtilization: 80

# Pod resource requests and limits
resources:
  requests:
    cpu: "100m"
    memory: "768Mi"
  limits:
    cpu: "500m"
    memory: "1Gi"

# environment variables for app - secret values come from vault
# provide last property value in quotes (Else ArgoCD fails to deploy 11/03/2022)
envVars: |
  - name: ENV
    value: "qa"
  - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
    value: http://otelc-collector.opentelemetry-operator.svc.cluster.local:4318/v1/traces
  - name: OTEL_LOGS_EXPORTER
    value: none
  - name: OTEL_EXPORTER_OTLP_METRICS_ENDPOINT
    value: http://otelc-gmp-collector.opentelemetry-operator.svc.cluster.local:4318/v1/metrics
  - name: OTEL_TRACES_SAMPLER
    value: always_on
  - name: OTEL_PROPAGATORS
    value: tracecontext,baggage,b3,b3multi
  - name: OTEL_EXPORTER_OTLP_PROTOCOL
    value: http/protobuf
  - name: OTEL_SERVICE_NAME
    value: omni-domain-oms-shipment-inbound-broker
  - name: OTEL_RESOURCE_ATTRIBUTES
    value: "deployment.environment=qa"
  - name: OTEL_INSTRUMENTATION_MICROMETER_ENABLED
    value: "true"

# environment variables for app - secret values come from vault
# vaultSecrets:


# env secrets to be referred as ENV variables. the secrets are found in Vault for the current namespace.
envSecrets: |
  - secretRef:
      name: oms-shipment-inbound-broker 
      optional: false
  
# leaving these commented in case we need them later
# extraVolumeMounts: |
#  - name: my-common-certs
#    mountPath: /opt/digital/microservices/omni-domain-oms-shipment-inbound-broker/certs
#    readOnly: true
#
# extraVolumes: |
#  - name: my-common-certs
#    secret:
#      secretName: my-common-certs
#      optional: false
