package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.specialty340b;

import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.genericpayload.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.Instant;

@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class Specialty340bOrderStatus {
    private String shipmentId;
    private String shipmentNo;
    private String event;
    private Instant eventTs;
    private String cancelOrRejectReason;
    // hard coded for 340B in ShipmentStatusUpdateController
    // may need to change in the future
    private String lob;
    private String eventSrc;

    private Specialty340bPackStatus packStatus;
    private Specialty340bShipStatus shipStatus;

    public PayloadOrderStatus toPayload() {
        PayloadOrderStatus payloadStatus = new PayloadOrderStatus();
        payloadStatus.setShipmentId(shipmentId);
        payloadStatus.setShipmentNo(shipmentNo);
        payloadStatus.setEvent(event);
        payloadStatus.setEventTs(eventTs);
        payloadStatus.setCancelOrRejectReason(cancelOrRejectReason);
        payloadStatus.setLob(lob);
        payloadStatus.setEventSrc(eventSrc);

        if(packStatus != null) {
            PayloadPackStatus payloadPackStatus = new PayloadPackStatus();

            for(Specialty340bRx rx : packStatus.getRx()) {
                PayloadRx payloadRx = new PayloadRx();
                payloadRx.setShipmentItemId(rx.getShipmentItemId());
                payloadRx.setRxNumber(rx.getRxNumber());
                payloadRx.setItemId(rx.getItemId());
                payloadRx.setQuantity(rx.getQuantity());
                payloadRx.setPackByUserId(rx.getPackByUserId());
                payloadRx.setPackByUserName(rx.getPackByUserName());
                payloadRx.setPackDt(rx.getPackDt());

                for (Specialty340bContainer container : rx.getContainers()) {
                    PayloadContainer payloadContainer = new PayloadContainer();
                    payloadContainer.setContainerId(container.getContainerId());
                    payloadContainer.setCheckByUserId(container.getCheckByUserId());
                    payloadContainer.setCheckDt(container.getCheckDt());
                    payloadContainer.setPickByUserId(container.getPickByUserId());
                    payloadContainer.setPickDt(container.getPickDt());
                    payloadContainer.setDispenseQuantityNb(container.getDispenseQuantityNb());

                    for (Specialty340bContainerContents contents : container.getContainerContents()) {
                        PayloadContainerContents payloadContents = new PayloadContainerContents();
                        payloadContents.setLotNb(contents.getLotNb());
                        payloadContents.setLotExpiryDt(contents.getLotExpiryDt());
                        payloadContents.setLotQuantity(contents.getLotQuantity());
                        payloadContents.setPickByUserId(contents.getPickByUserId());
                        payloadContents.setPickDt(contents.getPickDt());
                        payloadContents.setSerial(contents.getSerial());
                        payloadContainer.getContainerContents().add(payloadContents);
                    }
                    payloadRx.getContainers().add(payloadContainer);
                }
                payloadPackStatus.getRx().add(payloadRx);
            }
            payloadStatus.setPackStatus(payloadPackStatus);
        }

        if(shipStatus != null) {
            PayloadShipStatus payloadShipStatus = new PayloadShipStatus();
            payloadShipStatus.setTrackingNo(shipStatus.getTrackingNo());
            payloadShipStatus.setScac(shipStatus.getScac());
            payloadShipStatus.setShipDate(shipStatus.getShipDate());
            payloadShipStatus.setCarrierServiceCode(shipStatus.getCarrierServiceCode());
            payloadShipStatus.setEstimatedDeliveryDate(shipStatus.getEstimatedDeliveryDate());
            payloadShipStatus.setCost(shipStatus.getCost());
            payloadShipStatus.setWeight(shipStatus.getWeight());
            payloadShipStatus.setWeightUnit(shipStatus.getWeightUnit());
            payloadShipStatus.setShippingOptions(shipStatus.getShippingOptions());
            payloadStatus.setShipStatus(payloadShipStatus);
        }

        return payloadStatus;
    }
}
