# Start with a base image containing Java runtime
FROM cvsh.jfrog.io/cvsdigital-docker-local/devsecops/distroless/java-21-distroless:latest

# Add a volume pointing to /tmp
VOLUME /tmp

# CD app directory
ENV SERVICE_PATH=/opt/digital/microservices/omni-domain-oms-shipment-inbound-broker
WORKDIR	$SERVICE_PATH

# Add the application's jar to the container
COPY build/libs/omni-domain-oms-shipment-inbound-broker-exec.jar $SERVICE_PATH

# Copy OTEL auto instrumentation jar
COPY build/libs/opentelemetry-javaagent.jar $SERVICE_PATH

# Make port available to the world outside this container
EXPOSE 21000

# To avoid running as root
USER 9000:9000

# Run the jar file
ENTRYPOINT ["java", \
"-javaagent:opentelemetry-javaagent.jar", \
"-Xms128M", \
"-Djava.security.egd=file:/dev/./urandom ", \
"-Dspring.profiles.active=${ENV}", \
"-Dspring.config.location=/additional-config/application.yaml", \
"-Dlogging.config=/additional-config/logback-spring.xml",\
"-jar", \
"omni-domain-oms-shipment-inbound-broker-exec.jar"]
