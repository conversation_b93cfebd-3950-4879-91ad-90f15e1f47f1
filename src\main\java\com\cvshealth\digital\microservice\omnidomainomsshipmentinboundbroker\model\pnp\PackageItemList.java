package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PackageItemList {
    private String itemId;
    private String quantity;
    private String primeLineNumber;
}
