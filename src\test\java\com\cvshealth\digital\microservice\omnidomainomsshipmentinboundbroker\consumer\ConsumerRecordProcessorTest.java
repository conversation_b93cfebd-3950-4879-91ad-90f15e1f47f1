package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.consumer;

import com.cvshealth.digital.framework.service.kafka.exception.KafkaConsumerException;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.exception.model.ApiStatusCodes;
import com.cvshealth.digital.framework.starter.exception.model.FaultTypes;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.ShipmentUpdateEventRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.ShipmentUpdateEventResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.ShipmentInboundBrokerService;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogContext;
import com.cvshealth.digital.omni.library.oms.logging.util.OMSLogDiagnostics;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class ConsumerRecordProcessorTest {

    @Mock
    private ShipmentInboundBrokerService shipmentInboundBrokerService;

    @InjectMocks
    private ConsumerRecordProcessor consumerRecordProcessor;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void onMessage_success() throws ApiException, KafkaConsumerException {
        String eventType = "testEvent";
        String message = "testMessage";
        Header header = new RecordHeader("sourceSystemType", "testSourceSystem".getBytes());
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>("topic", 0, 0, null, message);
        consumerRecord.headers().add(header);

        ShipmentUpdateEventResponse response = new ShipmentUpdateEventResponse();
        response.setStatus("SUCCESS");

        when(shipmentInboundBrokerService.processShipmentUpdateRequest(any(ShipmentUpdateEventRequest.class)))
                .thenReturn(response);

        int result = consumerRecordProcessor.onMessage(eventType, consumerRecord);

        assertEquals(0, result);
    }

    @Test
    void onMessage_failure() throws ApiException, KafkaConsumerException {
        String eventType = "testEvent";
        String message = "testMessage";
        Header header = new RecordHeader("sourceSystemType", "testSourceSystem".getBytes());
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>("topic", 0, 0, null, message);
        consumerRecord.headers().add(header);

        ShipmentUpdateEventResponse response = new ShipmentUpdateEventResponse();
        response.setStatus("FAILURE");

        when(shipmentInboundBrokerService.processShipmentUpdateRequest(any(ShipmentUpdateEventRequest.class)))
                .thenReturn(response);

        int result = consumerRecordProcessor.onMessage(eventType, consumerRecord);

        assertEquals(-1, result);
    }

    @Test
    void onMessage_exception() throws ApiException, KafkaConsumerException {
        String eventType = "testEvent";
        String message = "testMessage";
        Header header = new RecordHeader("sourceSystemType", "testSourceSystem".getBytes());
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>("topic", 0, 0, null, message);
        consumerRecord.headers().add(header);

        when(shipmentInboundBrokerService.processShipmentUpdateRequest(any(ShipmentUpdateEventRequest.class)))
                .thenThrow(new ApiException(ApiStatusCodes.INTERNAL_SERVER_ERROR, FaultTypes.ERROR));

        int result = consumerRecordProcessor.onMessage(eventType, consumerRecord);

        assertEquals(-1, result);
    }

    @Test
    void onMessage_noSourceSystemTypeHeader() throws ApiException, KafkaConsumerException {
        String eventType = "testEvent";
        String message = "testMessage";
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>("topic", 0, 0, null, message);

        ShipmentUpdateEventResponse response = new ShipmentUpdateEventResponse();
        response.setStatus("SUCCESS");

        when(shipmentInboundBrokerService.processShipmentUpdateRequest(any(ShipmentUpdateEventRequest.class)))
                .thenReturn(response);

        int result = consumerRecordProcessor.onMessage(eventType, consumerRecord);

        assertEquals(0, result);
    }
}