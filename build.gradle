plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.7'
    id "io.freefair.lombok" version "8.6"
    id 'maven-publish'
    //id 'com.cvshealth.devex.otel.cvs-javaagent-instrumentation' version '2.0'
    id 'com.avast.gradle.docker-compose' version '0.17.8'
    id 'jacoco'
}

java {
    sourceCompatibility = java_version
    withSourcesJar()
}
configurations {
    annotationProcessor {
        extendsFrom implementation
    }
    developmentOnly {
        extendsFrom implementation
    }
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenLocal()
    maven {
        name = artifactory_repo
        url = artifactory_url
        //credentials(PasswordCredentials)
        credentials {
            username = System.getenv("ARTIFACTORY_USERNAME")
            password = System.getenv("ARTIFACTORY_TOKEN")
        }
    }
}
dependencies {
    // Platform BOM
    implementation(platform("com.cvshealth.digital.framework:digital-spring-boot-parent:${digital_spring_boot_framework_version}"))
    annotationProcessor platform("com.cvshealth.digital.framework:digital-spring-boot-parent:${digital_spring_boot_framework_version}")

    // CVS Dependencies
    implementation("com.cvshealth.digital.framework:digital-spring-boot-starter-web:${digital_spring_boot_framework_version}") {
        exclude group: 'io.micrometer', module: 'micrometer-registry-prometheus'
        exclude group: 'io.opentracing.contrib', module: 'opentracing-spring-jaeger-web-starter'
    }

    implementation "com.cvs.oms.common:oms-common-lib:0.0.1-20240409.191053-1"

    implementation "com.cvshealth.digital.framework:digital-service-kafka:${digital_spring_boot_framework_version}"

    implementation "io.github.resilience4j:resilience4j-spring-boot3:2.2.0"

    implementation "com.cvshealth.digital.oms.logging:omni-library-oms-logging:1.0.19"

    implementation "com.google.cloud:google-cloud-storage:2.31.0"
    // OTEL
    implementation 'io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.38.0'

    // tracing
    implementation 'io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:1.17.0-alpha'

    // spring boot test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // junit
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

    // mockito
    testImplementation 'org.mockito:mockito-core'

    // rest-assured
    testImplementation("io.rest-assured:rest-assured:5.5.0")

    // kafka
    testImplementation('org.springframework.kafka:spring-kafka')

    // mockwebserver
    testImplementation("com.squareup.okhttp3:mockwebserver")
}

tasks.named('test') {

    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

tasks.named("bootJar") {
    archiveFileName = "${rootProject.name}-exec.jar"
}

publishing {
    publications {
        "${name}"(MavenPublication) {
            artifactId = name
            from components.java
        }
    }
}

test {
    finalizedBy jacocoTestReport // report is always generated after tests run
}
test {
    testLogging
            {
                afterSuite
                        {
                            desc, result ->
                                if (result.testCount == 0)
                                    throw new GradleException("No tests were executed. Build failed.")
                        }
            }
}
jacocoTestReport {
    dependsOn test // tests are required to run before generating the report
    reports {
        xml.required = true
    }
}