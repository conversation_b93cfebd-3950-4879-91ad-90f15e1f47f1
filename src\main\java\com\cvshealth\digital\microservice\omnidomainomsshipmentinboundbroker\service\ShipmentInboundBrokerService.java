package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.constants.ApplicationConstant;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.ShipmentUpdate;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.processor.ShipmentEventProcessor;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogger;
import com.cvshealth.digital.omni.library.oms.logging.message.OMSLogMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * The ShipmentInboundBrokerService class.
 *
 * <AUTHOR> Brizard
 */
@RequiredArgsConstructor
@Service
public class ShipmentInboundBrokerService {

    private final ShipmentInboundBrokerServiceValidator shipmentInboundBrokerServiceValidator;
    private final List<ShipmentEventProcessor> shipmentEventProcessors;
    private final EventPublisherService eventPublisherService;
    private final ObjectMapper objectMapper;
    private static final OMSLogger logger = OMSLogger.getLogger(ShipmentInboundBrokerService.class);
    private final MeterRegistry meterRegistry;

    public ShipmentUpdateEventResponse processShipmentUpdateRequest(ShipmentUpdateEventRequest request) throws ApiException {

        OMSLogMessage logMessage = OMSLogMessage.builder()
                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                .operationName("process-update-request")
                .serviceLayer("service")
                .message("Processing received request")
                .tags(Map.of("request",request)).build();

        logger.debug(logMessage);

        ShipmentUpdateEventResponse response = new ShipmentUpdateEventResponse();

        shipmentInboundBrokerServiceValidator.validate(request);

        incrementReceivedCounter(request);

        shipmentEventProcessors.stream().filter(processor -> processor.canProcess(request.getSourceSystemType()))
                .findFirst().ifPresentOrElse(shipmentEventProcessor -> {

                    try {
                        // this will publish the message to the Kafka topic for Shipment Service
                        boolean successPublish = publishEvent(shipmentEventProcessor, request);

                        // this will call DMA which will send the message to Legacy OMS. this should happen
                        // regardless of error in previous step.
                        boolean successUpdates = sendUpdatesToDMA(shipmentEventProcessor, request);

                        if (successPublish && successUpdates) {
                            incrementSuccessCounter(request);
                        } else {
                            incrementFailedCounter(request);
                        }
                        response.setStatus(successPublish && successUpdates ? "SUCCESS": "FAILED");
                    } catch (Exception e) {
                        incrementFailedCounter(request);
                        OMSLogMessage errorLogMessage = OMSLogMessage.builder()
                                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                                .operationName("process-update-request")
                                .serviceLayer("service")
                                .message("Caught exception while processing request")
                                .tags(Map.of("eventType",request.getSourceSystemType(),"stacktrace",e)).build();
                        logger.error(errorLogMessage);
                        response.setStatus("FAILED");
                    }
                }, () -> {
                    OMSLogMessage errorLogMessage = OMSLogMessage.builder()
                            .appName("OMS-SHIPMENT-INBOUND-BROKER")
                            .operationName("process-update-request")
                            .serviceLayer("service")
                            .message("Caught exception while processing request")
                            .tags(Map.of("sourceSystemType", request.getSourceSystemType())).build();
                    logger.error(errorLogMessage);
                    response.setStatus("FAILED");
                });

        return response;
    }

    private boolean publishEvent(ShipmentEventProcessor shipmentEventProcessor, ShipmentUpdateEventRequest request) {

        try {
            // this will convert the request to the ShipmentUpdate object
            ShipmentUpdate shipmentUpdate = shipmentEventProcessor.processShipmentEvent(request.getMessage());

            // now publish the message to the Kafka topic
            String shipmentServiceRequest = objectMapper.writeValueAsString(shipmentUpdate);
            eventPublisherService.publishEvent("shipmentUpdateEvent", shipmentServiceRequest, getHeaders(shipmentUpdate, request));
        } catch (Exception e) {
            // TODO - we should retry this later
            logger.error("Caught exception while processing request: " + request, e);
            return false;
        }

        return true;
    }

    private Map<String, String> getHeaders(ShipmentUpdate shipmentUpdate, ShipmentUpdateEventRequest request) {

        Map<String, String> headers = new HashMap<>();

        headers.put(ApplicationConstant.HEADER_SOURCE, request.getSourceSystemType());
        headers.put(ApplicationConstant.HEADER_ACTION, "UPDATE_SHIPMENT_STATUS");
        headers.put(ApplicationConstant.HEADER_LINE_OF_BUSINESS, "Retail");
        headers.put(ApplicationConstant.HEADER_ORDER_TYPE, shipmentUpdate.getOrderType() != null ? shipmentUpdate.getOrderType() : "UNKNOWN");
        headers.put(ApplicationConstant.HEADER_ORDER_KEY, shipmentUpdate.getOrderNo() != null ? shipmentUpdate.getOrderNo() : "UNKNOWN");
        headers.put(ApplicationConstant.HEADER_TIMESTAMP, Instant.now().toString());
        headers.put(ApplicationConstant.HEADER_CONVERSATION_ID, UUID.randomUUID().toString());

        return headers;
    }

    private boolean sendUpdatesToDMA(ShipmentEventProcessor shipmentEventProcessor, ShipmentUpdateEventRequest request) {

        logger.debug("Sending updates to DMA: " + request.getMessage());

        try {
            shipmentEventProcessor.sendUpdates(request.getMessage());
        } catch (Exception e) {
            logger.error("Caught exception while sending updates to DMA: " + request, e);
            return false;
        }

        return true;
    }

    /**
     * Increment the counter for Received Shipment Update Events.
     *
     * This method uses Micrometer's {@link Counter} to count the number of
     * received shipment update events. The counter is registered with the
     * given {@link ShipmentUpdateEventRequest#getSourceSystemType()} as a tag.
     *
     * @param request the request used to derive the source system type
     */
    private void incrementReceivedCounter(ShipmentUpdateEventRequest request) {

        Counter shipmentUpdateEventCounterReceived = Counter.builder("shipment.update.event.received.counted")
                .description("Counter for Received Shipment Update Events")
                .tag("source_system_type", request.getSourceSystemType())
                .register(meterRegistry);

        shipmentUpdateEventCounterReceived.increment();
    }

    /**
     * Increment the counter for Success Shipment Update Events.
     *
     * This method uses Micrometer's {@link Counter} to count the number of
     * successful shipment update events. The counter is registered with the
     * given {@link ShipmentUpdateEventRequest#getSourceSystemType()} as a tag.
     *
     * @param request the request to process
     */
    private void incrementSuccessCounter(ShipmentUpdateEventRequest request) {

        Counter shipmentUpdateEventCounterSuccess = Counter.builder("shipment.update.event.success.counted")
                .description("Counter for Success Shipment Update Events")
                .tag("source_system_type", request.getSourceSystemType())
                .register(meterRegistry);

        shipmentUpdateEventCounterSuccess.increment();
    }

    /**
     * Increment the counter for Failed Shipment Update Events.
     *
     * This method uses Micrometer's {@link Counter} to count the number of
     * failed shipment update events. The counter is registered with the
     * given {@link ShipmentUpdateEventRequest#getSourceSystemType()} as a tag.
     *
     * @param request the request to process
     */
    private void incrementFailedCounter(ShipmentUpdateEventRequest request) {

        Counter shipmentUpdateEventCounterSuccess = Counter.builder("shipment.update.event.failed.counted")
                .description("Counter for Failed Shipment Update Events")
                .tag("source_system_type", request.getSourceSystemType())
                .register(meterRegistry);

        shipmentUpdateEventCounterSuccess.increment();
    }
}
