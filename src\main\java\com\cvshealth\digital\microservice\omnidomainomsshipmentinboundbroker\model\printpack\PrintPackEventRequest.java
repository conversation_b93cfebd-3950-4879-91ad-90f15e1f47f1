package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.printpack;

import com.cvshealth.digital.framework.starter.model.api.ApiRequest;
import lombok.EqualsAndHashCode;

import java.time.Instant;

@EqualsAndHashCode(callSuper = true)
@lombok.Data
public class PrintPackEventRequest extends ApiRequest {
    private String shipmentId;
    private String shipmentNo;
    private String event;
    private Instant eventTs;
    private String filePath;
}
