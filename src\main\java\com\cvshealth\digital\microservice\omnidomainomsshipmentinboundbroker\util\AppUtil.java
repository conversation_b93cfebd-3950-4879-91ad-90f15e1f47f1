package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.util;

import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

import java.util.Arrays;

@UtilityClass
public class AppUtil {


    public static String removeLeadingZero(String genericNo) {
        return StringUtils.hasText(genericNo) ? genericNo.trim().replaceFirst("^0*", "") : "";
    }

    public static String splitDate(String date) {
        if (StringUtils.hasText(date)) {
            String[] date1 = date.split("T");
            return StringUtils.hasText(Arrays.toString(date.split("T"))) ? date1[0].trim().replace("-", "") : "";
        }
        return null;
    }

    public static String splitTime(String time) {
        if (StringUtils.hasText(time)) {
            String[] time1 = time.split("T");
            return StringUtils.hasText(Arrays.toString(time1)) ? time1[1].trim().replace(":", "") : "";
        }
        return null;
    }
}
