package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.genericpayload;

import lombok.Data;

import java.time.Instant;

@Data
public class PayloadOrderStatus {
    private String shipmentId;
    private String shipmentNo;
    private String event;
    private Instant eventTs;
    private String cancelOrRejectReason;
    private String lob;
    private String eventSrc;

    private PayloadPackStatus packStatus;
    private PayloadShipStatus shipStatus;
}
