#namespace, app version and image details
appData:
  nameSpace: oms-qa
  dockerHub: cvsh.jfrog.io
  dockerImageGroupName: cvsdigital-docker/ghemu/cvs-health-source-code/omni-domain-oms-shipment-inbound-broker
  dockerImageName: omni-domain-oms-shipment-inbound-broker
  dockerImageTag: 1.0.26

#hpa settings - min and max replicas and minAvailable for PDB
hpaSettings:
  minAvailable: 1
  minReplicas: 1
  maxReplicas: 1
  averageUtilization: 80

# Pod resource requests and limits
resources:
  requests:
    cpu: "100m"
    memory: "768Mi"
  limits:
    cpu: "500m"
    memory: "1Gi"

#vaultSecrets:
#  - name: omni-domain-oms-shipment-inbound-broker-secrets
#    path: pay-and-get/oms/shipment-broker-outbound
#    keys:
#      - KAFKA_CONSUMER_KEY
#      - KAFKA_CONSUMER_PWD
#      - KAFKA_PRODUCER_KEY
#      - KAFKA_PRODUCER_PWD
#      - JASYPT_KEY
#      - STORE_CERTIFICATE
#      - STORE_CERTIFICATE_PASSWORD

# environment variables for app - secret values come from vault
# provide last property value in quotes (Else ArgoCD fails to deploy 11/03/2022)
envVars: |
  - name: ENV
    value: specialty, qa

# env secrets to be referred as ENV variabes.
envSecrets: |
  - secretRef:
      name: oms-shipment-inbound-broker
      optional: false
#
#extraVolumeMounts: |
#  - name: my-common-certs
#    mountPath: /opt/digital/microservices/omni-domain-oms-shipment-service/certs
#    readOnly: true
#
#extraVolumes: |
#  - name: my-common-certs
#    secret:
#      secretName: my-common-certs
#      optional: false

# gcp workload identity
workloadIdentity:
  gcpProject: digital-mfe-qa
  serviceAccountName: speciality-order-account
  serviceAccountEmail: <EMAIL>

  
