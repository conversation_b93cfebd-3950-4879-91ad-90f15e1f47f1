package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.config.specialty;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Data
@Configuration
@ConfigurationProperties(prefix = "oms.gcp")
@Profile("specialty")
public class GcpProperties {
    private String projectName;
    private Storage storage;

    @Data
    public static class Storage {
        private String bucketName;
    }
}