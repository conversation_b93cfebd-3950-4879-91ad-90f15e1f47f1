package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.specialty340b;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class Specialty340bShipStatus {
    private String trackingNo;
    private String scac;
    private String shipDate;
    private String carrierServiceCode;
    private String estimatedDeliveryDate;
    private Double cost;
    private Double weight;
    private String weightUnit;
    private ArrayList<String> shippingOptions;
}
