package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.processor;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.ShipmentUpdate;

public interface ShipmentEventProcessor {

    ShipmentUpdate processShipmentEvent(String message) throws ApiException;

    void sendUpdates(String message) throws ApiException;

    boolean canProcess(String eventSourceSystem);
}
