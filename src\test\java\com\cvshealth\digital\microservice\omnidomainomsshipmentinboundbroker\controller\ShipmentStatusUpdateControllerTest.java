package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.controller;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Map;

public class ShipmentStatusUpdateControllerTest {

    private ShipmentStatusUpdateController controller;
    private EventPublisherService mockEventPublisherService;

    @BeforeEach
    public void setUp() {
        mockEventPublisherService = Mockito.mock(EventPublisherService.class);
        controller = new ShipmentStatusUpdateController(mockEventPublisherService, new ObjectMapper());
    }

    @Test
    public void test_shippingUpdatesAllFields() throws ApiException {
        Map<String, String> headers = Map.of(
            "lob", "SPECIALTY",
            "shipment-id", "12345",
            "shipment-no", "4353453",
            "event", "PACKED_EVENT",
            "event-src", "EXTERNAL",
            "event-ts", "1747173856"
        );

        String inbound = """
{"shipmentId":"12345","shipmentNo":"4353453","event":"PACKED_EVENT","eventTs":"1747173856","cancelOrRejectReason":"out of stock","packStatus":{"rx":[{"shipmentItemId":423434,"rxNumber":"31083889","itemId":"44080","quantity":700.0,"packByUserId":"JD","packByUserName":"JohnDoe","packDt":"2025-03-31","containers":[{"containerId":"200021416206","checkByUserId":"u012371","checkDt":"2025-03-31","pickByUserId":"C767314","pickDt":"2025-03-31","dispenseQuantityNb":700.0,"containerContents":[{"lotNb":"1271335","lotExpiryDt":"2025-03-31","lotQuantity":700.0,"pickByUserId":"C767314","pickDt":"2025-03-31","serial":""}]}]}]},"shipStatus":{"trackingNo":"1Z62319533767945628","scac":"UPS","shipDate":"2025-03-27T12:49:05.193","carrierServiceCode":"","estimatedDeliveryDate":"2025-03-31","cost":5.83,"weight":0.867,"weightUnit":"LB","shippingOptions":["SIGNATURE_CONFIRMATION","SATURDAY_DELIVERY"]}}""";
        String outbound = """
{"shipmentId":"12345","shipmentNo":"4353453","event":"PACKED_EVENT","eventTs":"1747173856","cancelOrRejectReason":"out of stock","lob":"SPECIALTY","eventSrc":"EXTERNAL","packStatus":{"rx":[{"shipmentItemId":423434,"rxNumber":"31083889","itemId":"44080","quantity":700.0,"packByUserId":"JD","packByUserName":"JohnDoe","packDt":"2025-03-31","containers":[{"containerId":"200021416206","checkByUserId":"u012371","checkDt":"2025-03-31","pickByUserId":"C767314","pickDt":"2025-03-31","dispenseQuantityNb":700.0,"containerContents":[{"lotNb":"1271335","lotExpiryDt":"2025-03-31","lotQuantity":700.0,"pickByUserId":"C767314","pickDt":"2025-03-31","serial":""}]}]}]},"shipStatus":{"trackingNo":"1Z62319533767945628","scac":"UPS","shipDate":"2025-03-27T12:49:05.193","carrierServiceCode":"","estimatedDeliveryDate":"2025-03-31","cost":5.83,"weight":0.867,"weightUnit":"LB","shippingOptions":["SIGNATURE_CONFIRMATION","SATURDAY_DELIVERY"]}}""";

        Mockito.when(mockEventPublisherService.publishEvent("shipmentStatusUpdateEvent", inbound, null)).thenReturn(new ApiResponse());

        ApiResponse response = controller.shippingUpdates(inbound);

        Assertions.assertNotNull(response);

        Mockito.verify(mockEventPublisherService, Mockito.times(1)).publishEvent("shipmentStatusUpdateEvent", outbound, headers);
    }

    @Test
    public void test_shippingUpdatesExtraAttributes() throws ApiException {
        Map<String, String> headers = Map.of(
                "lob", "SPECIALTY",
                "shipment-id", "12345",
                "shipment-no", "4353453",
                "event", "PACKED_EVENT",
                "event-src", "EXTERNAL",
                "event-ts", "1747173856"
        );

        String requestExtraAttributes = """
{"abc":123, "shipmentId":"12345","shipmentNo":"4353453","event":"PACKED_EVENT","eventTs":"1747173856","cancelOrRejectReason":"out of stock","packStatus":{"rx":[{"shipmentItemId":423434,"rxNumber":"31083889","itemId":"44080","quantity":700.0,"packByUserId":"JD","packByUserName":"JohnDoe","packDt":"2025-03-31","containers":[{"containerId":"200021416206","checkByUserId":"u012371","checkDt":"2025-03-31","pickByUserId":"C767314","pickDt":"2025-03-31","dispenseQuantityNb":700.0,"containerContents":[{"lotNb":"1271335","lotExpiryDt":"2025-03-31","lotQuantity":700.0,"pickByUserId":"C767314","pickDt":"2025-03-31","serial":""}]}]}]},"shipStatus":{"trackingNo":"1Z62319533767945628","scac":"UPS","shipDate":"2025-03-27T12:49:05.193","carrierServiceCode":"","estimatedDeliveryDate":"2025-03-31","cost":5.83,"weight":0.867,"weightUnit":"LB","shippingOptions":["SIGNATURE_CONFIRMATION","SATURDAY_DELIVERY"]}}""";
        String request = """
{"shipmentId":"12345","shipmentNo":"4353453","event":"PACKED_EVENT","eventTs":"1747173856","cancelOrRejectReason":"out of stock","lob":"SPECIALTY","eventSrc":"EXTERNAL","packStatus":{"rx":[{"shipmentItemId":423434,"rxNumber":"31083889","itemId":"44080","quantity":700.0,"packByUserId":"JD","packByUserName":"JohnDoe","packDt":"2025-03-31","containers":[{"containerId":"200021416206","checkByUserId":"u012371","checkDt":"2025-03-31","pickByUserId":"C767314","pickDt":"2025-03-31","dispenseQuantityNb":700.0,"containerContents":[{"lotNb":"1271335","lotExpiryDt":"2025-03-31","lotQuantity":700.0,"pickByUserId":"C767314","pickDt":"2025-03-31","serial":""}]}]}]},"shipStatus":{"trackingNo":"1Z62319533767945628","scac":"UPS","shipDate":"2025-03-27T12:49:05.193","carrierServiceCode":"","estimatedDeliveryDate":"2025-03-31","cost":5.83,"weight":0.867,"weightUnit":"LB","shippingOptions":["SIGNATURE_CONFIRMATION","SATURDAY_DELIVERY"]}}""";

        Mockito.when(mockEventPublisherService.publishEvent("shipmentStatusUpdateEvent", requestExtraAttributes, null)).thenReturn(new ApiResponse());

        ApiResponse response = controller.shippingUpdates(requestExtraAttributes);

        Assertions.assertNotNull(response);

        Mockito.verify(mockEventPublisherService, Mockito.times(1)).publishEvent("shipmentStatusUpdateEvent", request, headers);
    }

    @Test
    public void test_shippingUpdatesMissingRequiredAttribute() throws ApiException {
        String requestMissingRequiredAttributes = """
{"shipmentId":"12345","shipmentNo":"4353453","eventTs":"1747173856","cancelOrRejectReason":"out of stock","packStatus":{"rx":[{"shipmentItemId":423434,"rxNumber":"31083889","itemId":"44080","quantity":700.0,"packByUserId":"JD","packByUserName":"JohnDoe","packDt":"2025-03-31","containers":[{"containerId":"200021416206","checkByUserId":"u012371","checkDt":"2025-03-31","pickByUserId":"C767314","pickDt":"2025-03-31","dispenseQuantityNb":700.0,"containerContents":[{"lotNb":"1271335","lotExpiryDt":"2025-03-31","lotQuantity":700.0,"pickByUserId":"C767314","pickDt":"2025-03-31","serial":""}]}]}]},"shipStatus":{"trackingNo":"1Z62319533767945628","scac":"UPS","shipDate":"2025-03-27T12:49:05.193","carrierServiceCode":"","estimatedDeliveryDate":"2025-03-31","cost":5.83,"weight":0.867,"weightUnit":"LB","shippingOptions":["SIGNATURE_CONFIRMATION","SATURDAY_DELIVERY"]}}""";

        Mockito.when(mockEventPublisherService.publishEvent("shipmentStatusUpdateEvent", requestMissingRequiredAttributes, null)).thenReturn(new ApiResponse());

        // event attribute is missing, which is expected to fail and throw an exception
        try {
            controller.shippingUpdates(requestMissingRequiredAttributes);
        } catch(ApiException e) {
            return;
        }

        Assertions.fail();
    }
}
