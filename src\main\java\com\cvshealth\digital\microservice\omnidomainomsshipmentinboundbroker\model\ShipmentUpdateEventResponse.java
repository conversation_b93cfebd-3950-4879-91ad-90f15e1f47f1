package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model;

import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * The ShipmentUpdateEventResponse class.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@lombok.Data
public class ShipmentUpdateEventResponse extends ApiResponse {
    private String status;
}
