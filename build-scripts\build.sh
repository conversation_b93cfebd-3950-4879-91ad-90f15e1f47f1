#1/bin/bash

echo "in build.sh"

#access the branch name
BRANCH_NAME="${GITHUB_REF_NAME}"
echo "Running on branch: $BRANCH_NAME"

if [ "$BRANCH_NAME" = "main" ]; then
    echo "main branch"
else
    #extract prefix based on branch name
    PREFIX=$(echo $BRANCH_NAME | cut -d'_' -f1)
    echo "PREFIX is $prefix"
    TECHX_COMMON_VERSION="${PREFIX}-SNAPSHOT"
fi

#run build command
echo "TECHX_COMMON_VERSION is $TECHX_COMMON_VERSION"
mvn -q clean package -Dtechx.common.version=$TECHX_COMMON_VERSION -s ~/.m2/settings.xml
echo "MAVEN_OPTS=-Dtechx.common.version=$TECHX_COMMON_VERSION" >> $GITHUB_ENV