package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.controller;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.exception.model.ApiStatusCodes;
import com.cvshealth.digital.framework.starter.exception.model.FaultTypes;
import com.cvshealth.digital.framework.starter.exception.model.ServiceStatusCodes;
import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.ShipmentUpdateEventResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.genericpayload.PayloadOrderStatus;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.specialty340b.Specialty340bOrderStatus;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogger;
import com.cvshealth.digital.omni.library.oms.logging.message.OMSLogMessage;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.lang.invoke.MethodHandles;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@RequestMapping("${service.context-path}")
public class ShipmentStatusUpdateController {
    private static final OMSLogger logger = OMSLogger.getLogger(MethodHandles.lookup().lookupClass());
    private final EventPublisherService eventPublisherService;
    private final ObjectMapper objectMapper;

    @Tag(name = "shippingUpdates")
    @Operation(summary = "handles shipment update event request", description = "handle a shipment update event request")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Success Response",      content = { @Content(mediaType = "application/json", schema = @Schema (implementation = ShipmentUpdateEventResponse.class))}),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Bad Request",           content = { @Content(mediaType = "application/json", schema = @Schema (implementation = ApiResponse.class))}),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "422", description = "Error processing the request", content = { @Content(mediaType = "application/json", schema = @Schema (implementation = ApiResponse.class))}),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal Server Error", content = { @Content(mediaType = "application/json", schema = @Schema (implementation = ApiResponse.class))})
    })
    @PostMapping(value = "/shippingUpdates", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse shippingUpdates(@RequestBody String request) throws ApiException {

        OMSLogMessage.OMSLogMessageBuilder logMessage = OMSLogMessage.builder()
                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                .operationName("shippingUpdates-endpoint")
                .serviceLayer("controller")
                .message("Received request");
        logger.entry(logMessage.build());

        String event;
        String shipmentNo;
        try {
            JsonNode root = objectMapper.readTree(request);
            event = root.findValue("event").asText();
            shipmentNo = root.findValue("shipmentNo").asText();
        } catch(Exception e) {
            logger.error(logMessage.message("Failed to parse JSON request string - event, eventSrc, lob, and shipmentNo attributes are required").build(), e);
            throw new ApiException(ApiStatusCodes.BAD_REQUEST, FaultTypes.ERROR);
        }

        try {
            Specialty340bOrderStatus status = objectMapper.readValue(request, Specialty340bOrderStatus.class);
            status.setLob("SPECIALTY");
            status.setEventSrc("EXTERNAL");

            PayloadOrderStatus payload = status.toPayload();
            String json = objectMapper.writeValueAsString(payload);

            try {
                Map<String, String> headers = Map.of(
                    "lob", status.getLob(),
                    "shipment-id", status.getShipmentId(),
                    "shipment-no", status.getShipmentNo(),
                    "event", status.getEvent(),
                    "event-src", status.getEventSrc(),
                    "event-ts", String.valueOf(status.getEventTs().getEpochSecond())
                );
                eventPublisherService.publishEvent("shipmentStatusUpdateEvent", json, headers);
            } catch(ApiException e) {
                logMessage.message("Caught exception while publishing payload to kafka")
                        .tags(Map.of("event", event, "shipmentNo", shipmentNo));
                logger.error(logMessage.build(), e);
                return new ApiResponse(ServiceStatusCodes.GENERIC_ERROR, ServiceStatusCodes.SERVICE_ERROR);
            }

            logMessage.message("Successfully processed request")
                    .tags(Map.of("event", event, "shipmentNo", shipmentNo));
            logger.debug(logMessage.build());
            return new ApiResponse(ServiceStatusCodes.STATUS_CODE_0000_SUCCESS, ServiceStatusCodes.SUCCESS);
        } catch (Exception e) {
            logMessage.message("Caught exception while processing request")
                    .tags(Map.of("event", event, "shipmentNo", shipmentNo));
            logger.error(logMessage.build(), e);
            throw new ApiException(ApiStatusCodes.BAD_REQUEST, FaultTypes.ERROR);
        }
    }
}
