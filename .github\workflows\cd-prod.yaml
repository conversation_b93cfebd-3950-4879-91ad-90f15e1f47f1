name: App Deployment

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - '**/deploy-configs/oms-prod/**'
      - '**/deploy-configs/oms-prod-dr/**'
      - '**/deploy-configs/common/**'

permissions:
  id-token: write
  contents: read
  pull-requests: write

jobs:
  detect-changes:
    runs-on:
      group: cvs-linux-self-hosted
    outputs:
      ENV_NAME: ${{ steps.set-env.outputs.ENV_NAME }}
      HELM_FILE_VERSION: ${{ steps.set-env.outputs.HELM_FILE_VERSION }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect changes
        id: changes
        uses: dorny/paths-filter@v3
        with:
          base: ${{ github.ref_name }}
          filters: |
            oms-prod_v1:
            - '**/deploy-configs/oms-prod/values/v1.yaml'
            oms-prod-dr_v1:
            - '**/deploy-configs/oms-prod-dr/values/v1.yaml'

      - name: Set Environment Variables
        id: set-env
        run: |

          if [ "${{ steps.changes.outputs.oms-prod_v1 }}" = "true" ]; then
            echo "ENV_NAME=oms-prod" >> $GITHUB_OUTPUT
            echo "HELM_FILE_VERSION=v1" >> $GITHUB_OUTPUT
          elif [ "${{ steps.changes.outputs.oms-prod-dr_v1 }}" = "true" ]; then
              echo "ENV_NAME=oms-prod-dr" >> $GITHUB_OUTPUT
              echo "HELM_FILE_VERSION=v1" >> $GITHUB_OUTPUT
          fi

  deploy:
    uses: cvs-health-source-code/gitops-cd-workflow/.github/workflows/helm-deploy.yaml@v1
    needs: [detect-changes]
    secrets: inherit
    with:
      app-name: "omni-domain-oms-shipment-inbound-broker"
      environment: ${{ needs.detect-changes.outputs.ENV_NAME }}
      helm-template-repo: ${{ vars.HELM_TEMPLATE_REPO }}
      helm-file-version: ${{ needs.detect-changes.outputs.HELM_FILE_VERSION }}
      helm-template-branch: ${{ vars.HELM_TEMPLATE_BRANCH }}
      canary-weight: "0"
