package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model;

import com.cvshealth.digital.framework.starter.model.api.ApiRequest;
import lombok.EqualsAndHashCode;

/**
 * The ShipmentUpdateEventRequest class.
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@lombok.Data
public class ShipmentUpdateEventRequest extends ApiRequest {
    private String sourceSystemType;
    private String message;
}
