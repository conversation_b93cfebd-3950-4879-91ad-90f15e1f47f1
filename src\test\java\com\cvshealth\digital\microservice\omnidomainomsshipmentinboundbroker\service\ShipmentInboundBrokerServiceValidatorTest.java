package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.ShipmentUpdateEventRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;

public class ShipmentInboundBrokerServiceValidatorTest {

    @InjectMocks
    private ShipmentInboundBrokerServiceValidator validator;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testValidate_Success() throws ApiException {
        // Arrange
        ShipmentUpdateEventRequest validInput = new ShipmentUpdateEventRequest();
        validInput.setMessage("{ \"shipmentId\": \"12345\", \"status\": \"DELIVERED\" }");
        validInput.setSourceSystemType("PNP");

        // Act
        ShipmentUpdateEventRequest result = validator.validate(validInput);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testValidate_Failure_noSourceSystem() {

        ShipmentUpdateEventRequest invalidInput = new ShipmentUpdateEventRequest();
        invalidInput.setMessage("{ \"shipmentId\": \"12345\", \"status\": \"DELIVERED\" }");
        invalidInput.setSourceSystemType("");

        assertThrows(ApiException.class, () -> validator.validate(invalidInput));
    }

    @Test
    public void testValidate_Failure_noMessage() {

        ShipmentUpdateEventRequest invalidInput = new ShipmentUpdateEventRequest();
        invalidInput.setMessage(null);
        invalidInput.setSourceSystemType("PNP");

        assertThrows(ApiException.class, () -> validator.validate(invalidInput));
    }

    @Test
    public void testValidate_Failure_emptyString() {

        ShipmentUpdateEventRequest invalidInput = new ShipmentUpdateEventRequest();
        invalidInput.setMessage("");
        invalidInput.setSourceSystemType("PNP");

        assertThrows(ApiException.class, () -> validator.validate(invalidInput));
    }

    @Test
    public void testValidate_Failure_emptyMessage() {

        ShipmentUpdateEventRequest invalidInput = new ShipmentUpdateEventRequest();
        invalidInput.setMessage("{}");
        invalidInput.setSourceSystemType("PNP");

        assertThrows(ApiException.class, () -> validator.validate(invalidInput));
    }
}
