package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service;

import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.config.specialty.GcpProperties;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.util.Base64Util;
import com.google.cloud.storage.*;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Profile("specialty")
@Service
public class GCSService {

    private final GcpProperties gcpProperties;

    private static final Logger logger = LoggerFactory.getLogger(GCSService.class);

    public GCSService(GcpProperties gcpProperties) {
        this.gcpProperties = gcpProperties;
    }


    public String getEncodedContent(String filePath) {
        String content = downloadFileAsString(filePath);
        try {
            if (content == null) {
                return null;
            }
            return Base64Util.encode(content);

        } catch (StorageException se) {
            if (se.getCode() == 404) {
                return null; // or rethrow
            }
            throw se;
        }
    }

    public byte[] downloadFile(String pathinBucket) {
        String bucketName = gcpProperties.getStorage() != null ? gcpProperties.getStorage().getBucketName() : null;
        logger.info("Attempting to download file from bucket: {}, path: {}", bucketName, pathinBucket);

        Storage storage = getStorageObject();
        logger.info("Obtained storage object from bucket: {}", bucketName);

        if (!StringUtils.hasText(bucketName) || !StringUtils.hasText(pathinBucket)) {
            logger.warn("Bucket name or path is empty. bucketName: {}, pathinBucket: {}", bucketName, pathinBucket);
            return null;
        }
        try {
            Blob blob = storage.get(BlobId.of(bucketName, pathinBucket));
            if (blob == null || !blob.exists()) {
                logger.error("File not found in GCS bucket: {}, path: {}", bucketName, pathinBucket);
                return null;
            }
            logger.info("Successfully downloaded file from GCS: {}", pathinBucket);
            return blob.getContent();
        } catch (StorageException se) {
            if (se.getCode() == 404) {
                logger.error("StorageException 404: File not found in GCS bucket: {}, path: {}", bucketName, pathinBucket);
                return null;
            }
            throw se;
        } catch (Exception e) {
            logger.error("Unexpected error downloading file", e);
            throw new RuntimeException(e);
        }
    }

    public String downloadFileAsString(String pathinBucket) {
        logger.debug("Downloading file as string from path: {}", pathinBucket);
        byte[] content = downloadFile(pathinBucket);
        if (content == null) {
            logger.warn("No content found for path: {}", pathinBucket);
            return null;
        }
        logger.info("Successfully downloaded file as String from GCS: {}", pathinBucket);
        return new String(content, java.nio.charset.StandardCharsets.UTF_8);
    }

    private Storage getStorageObject () {
        return StorageOptions.newBuilder ().setProjectId (gcpProperties.getProjectName()).build ().getService ();
    }


}