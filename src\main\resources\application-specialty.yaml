# --------------------
# Application Configuration
# Prefix: app.*
# --------------------
app:
  events:
    shipmentStatusUpdateEvent:
      enabled: true
      topic: digitalomni-oms-specialty-shipment-status-update-event-dev

# --------------------
# Clients Configuration
# Prefix: clients.*
# --------------------
clients:
  dma:
    url: https://internal-qa-ohs-ms.cvshealth.com/delivery/publish/v1/pnp/event
    timeout: 5

# --------------------
# Spring Boot Framework / Service Configuration
# Prefix: service.*
# --------------------
service:
  context-path: /microservices/omni-domain-oms-shipment-specialty-inbound-broker
  logExceptionHandlerError: true
  #  encrypt:
  #    method: JASYPT
  #    jasypt:
  #      key: ${JASYPT_KEY}
  logging:
    #   Supported mode: CVSEVENT, LOGAPP
    mode: CVSEVENT, LOGAPP
    #   Supported destinations: CONSOLE, HTTP
    #   For HTTP, set log-app.url
    destination:
      - CONSOLE
    log-app:
      url: ""
    #   List of events to ignore while logging. Valid: ENTRY, EXIT, INFO, EXIT
    ignore-events:
      - ENTRY
    excluded-endpoints:
      - "/actuator*/**"
      - "/swagger*/**"
      - "/health*/**"
      - "/v3/api*/**"
      - "/metrics/**"
      - "/microservices/omni-domain-oms-shipment-specialty-inbound-broker/actuator*/**"
      - "/microservices/omni-domain-oms-shipment-specialty-inbound-broker/swagger*/**"
      - "/microservices/omni-domain-oms-shipment-specialty-inbound-broker/health*/**"


  # --------------------
  # Kafka client configuration
  # Prefix: service.kafka.*
  # For more details, refer: https://cvsdigital.atlassian.net/wiki/spaces/SBREF/pages/**********/digital-service-kafka
  # --------------------
  kafka:
    consumers:
      inbound-shipment-update-event:
        enabled: true
        event-type: inbound-status-update-event
        consumer-class: com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.consumer.AckNackEventProcessor
        client-id: omni-domain-oms-shipment-inbound-broker
        group-id: digitalomni-oms-specialty-shipment-inbound-broker
        bootstrap-servers: localhost:29092
        topics: digitalomni-oms-specialty-shipment-status-inbound-broker-event-dev
        commit-style: AT_LEAST_ONCE_HIGH_THROUGHPUT
        max-poll-interval: 5m
        max-poll-records: 50
        auto-offset-reset: latest
        consumer-concurrency: 1
        consumer-record-processor-concurrency: 1
        initial-delay: 5s
        properties:
          security:
            protocol: SASL_PLAINTEXT
          ssl:
            endpoint:
              identification:
                algorithm: ""
          sasl:
            mechanism: PLAIN
            jaas:
              config: org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin123";
    producers:
      default:
        enabled: true
        client-id: omni-domain-oms-shipment-inbound-broker
        bootstrap-servers: localhost:29092
        properties:
          security:
            protocol: SASL_PLAINTEXT
          ssl:
            endpoint:
              identification:
                algorithm: ""
          sasl:
            mechanism: PLAIN
            jaas:
              config: org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin123";

# --------------------
# REST Service configuration
# To use RestService, set rest.enabled=true and provide endpoints
# Prefix: rest.*
# --------------------
rest:
  enabled: false
  endpoint:
    service-name:
      operation-name:
        url: ""
        method: POST
        headers:
          accept: "application/json"
          content-type: "application/json"

# --------------------
# Server Configuration
# Prefix: server.*
# --------------------
server:
  port: 8083
  max-http-request-header-size: 80KB

# --------------------
# Spring Configuration including datasource
# Prefix: spring.*
# --------------------
spring:
  application:
    name: omni-domain-oms-shipment-specialty-inbound-broker
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  main:
    banner-mode: off
  jpa:
    open-in-view: false
  threads:
    virtual:
      enabled: true

# --------------------
# Api Information
# Prefix: info.*
# --------------------
info:
  app:
    name: omni-domain-oms-shipment-specialty-inbound-broker
    description: omni-domain-oms-shipment-inbound-broker spring boot microservice
    version: 1.0.0-SANPSHOT
    encoding: UTF-8
    java:
      source: 21
      target: 21

# --------------------
# Swagger configuration
# Prefix: springdoc.*
# --------------------
springdoc:
  packagesToScan: com.cvshealth
  pathsToMatch: /microservices/omni-domain-oms-shipment-specialty-inbound-broker/**
  swagger-ui:
    enabled: true
    path: /microservices/omni-domain-oms-shipment-specialty-inbound-broker/swagger/swagger-ui.html
  api-docs:
    enabled: true
    path: /microservices/omni-domain-oms-shipment-specialty-inbound-broker/swagger/api-docs

# ---------------------------
# Resilience4j configuration
# ---------------------------
resilience4j:
  retry:
    instances:
      dma-service:
        maxAttempts: 3
        waitDuration: 500ms


# --------------------
# Actuator monitoring
# Prefix: management.*
# --------------------
management:
  security:
    enabled: false
  endpoint:
    metrics:
      enabled: true
    health:
      show-details: always
      probes:
        enabled: true
        add-additional-paths: true
    httptrace:
      excluded-endpoints: /favicon.ico,/actuator/**,/metrics/**,/microservices/omni-domain-oms-shipment-specialty-inbound-broker/actuator/**
  endpoints:
    web:
      base-path: /microservices/omni-domain-oms-shipment-specialty-inbound-broker/actuator
      exposure:
        include:
          - health
  metrics:
    export:
      prometheus:
        enabled: false
    percentiles-histogram:
      http:
        server:
          requests: true
    distribution:
      sla:
        http:
          server:
            requests: 50ms

