package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.mapper;

import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.printpack.PrintPackEventRequest;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Component
public class PrintPackEventMapper {

    public Map<String, String> printPackInput(PrintPackEventRequest request) {
        return Map.of(
                "lob", "SPECIALTY",
                "shipment-id", request.getShipmentId(),
                "shipment-no", request.getShipmentNo(),
                "event", request.getEvent(),
                "event-src", "340B",
                "event-ts", String.valueOf(request.getEventTs().getEpochSecond())
        );

    }
}
