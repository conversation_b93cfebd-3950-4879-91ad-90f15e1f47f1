package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShipmentItem {
    private int orderLineNo;
    private int subLineNo;
    private String itemId;
    private int orderedQuantity;
    private int shortedQty;
    private int packedQty;
    private int cancelledQty;
    private String lineCancellationReason;
    private String upcNumber;
    private String managerOverrideId;
    private String cancelledDate;
    private String fillNumber;
    private String fillSeqNum;
    private String fillVerNum;
    private String rxNumber;
    private String substitutedItemId;
    private String upcSubstituteItem;
    private Double tax;
    private Double storeUnitPrice;
    private Double unitPrice;
}
