package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import static com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Item {
    private String itemId;
    private String pickedQty;
    private String orderedQty;
    private String unitPrice;
    private String itemShortDesc;
    private String rxNum;
    private String refilNum;
    private String partialFillSeqNum;
    private String fillVersionNumber;
    private String primeLineNumber;
    private String substitutedItemId;
    private String upcSubstituteItem;
    private String tax;
    private String cancelledDate;
    private String cancelledReasonDesc;
    private String storeUnitPrice;
    private String managerOverrideId;
}
