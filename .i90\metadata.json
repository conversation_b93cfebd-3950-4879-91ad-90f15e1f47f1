{"applicationId": "ef7880ba-1968-4b11-a75b-f89a9b9be31f", "applicationName": "omni-domain-oms-shipment-inbound-broker", "applicationDesc": "The OMS Shipment Inbound Broker is a service used to process status events from clients.", "appFramework": "SPRINGBOOT", "appFeatures": ["WEB", "KAFKA", "KAFKA_PRODUCER", "KAFKA_CONSUMER"], "authType": "NO_AUTH", "lineOfBusiness": "PHARMACY", "appName": "Omni Pharmacy", "clientName": "fullfillment_and_notifications", "minResponseTimeInMs": 100, "maxResponseTimeInMs": 5000, "averageResponseTimeInMs": 500, "responseTimeThresholdInMs": 1000, "minTps": 10, "maxTps": 50, "avgTps": 15, "circuitBreakerErrorRate": 10, "memberEventInd": false, "primaryDataCenter": "GCP_EAST", "secondaryDataCenter": "GCP_WEST", "status": "PENDING", "active": true, "updatedBy": "<PERSON><PERSON><PERSON>", "updatedDate": "2025-02-19T16:06:28.344707865", "createdBy": "<PERSON>", "createdDate": "2025-02-18T19:39:13.118", "alerts": {"thresholdApiAlertsInd": true, "thresholdApiAlertsSeverity": "WARNING", "thresholdApiAlertsValue": 2000, "availabilityApiAlertsInd": true, "availabilityApiAlertsSeverity": "WARNING", "availabilityApiAlertsValue": 99, "tpsApiAlertsInd": true, "tpsApiAlertsSeverity": "WARNING", "tpsApiAlertsValue": 100, "channel": "slack", "groupName": "i90-OnboardingEligiblity", "owner": "app"}, "experimentalFlag": false}