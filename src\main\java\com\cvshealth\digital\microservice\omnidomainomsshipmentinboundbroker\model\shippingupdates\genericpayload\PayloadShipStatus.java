package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.genericpayload;

import lombok.Data;

import java.util.ArrayList;

@Data
public class PayloadShipStatus {
    private String trackingNo;
    private String scac;
    private String shipDate;
    private String carrierServiceCode;
    private String estimatedDeliveryDate;
    private Double cost;
    private Double weight;
    private String weightUnit;
    private ArrayList<String> shippingOptions;
}
