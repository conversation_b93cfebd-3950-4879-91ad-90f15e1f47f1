package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShipmentUpdateDispositionData {
    private String priceSign;
    private String priceSource;
    private String mfTxnRecordCd;
    private String mfTxnTypeInd;
    private String mfTxnTypeCd;
    private String mfPollDt;
    private String mfOriginalTxn;
    private String posSaleTimeStamp;
    private String registerNo;
    private String txnNo;
    private String txnType;
    private String userId;
    private String mfSku;
    private String registerNum;
    private String registerTxnSeqNum;
}
