package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import static com.fasterxml.jackson.annotation.JsonInclude.Include;
@JsonInclude(Include.NON_NULL)
@lombok.Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestPayloadData{
    private Data data;
    private AdditionalData additionalData;
}