package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service;

import com.cvshealth.digital.framework.starter.exception.ApiErrors;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.exception.api.ApiRequiredFieldMissingException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.ShipmentUpdateEventRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.ShipmentUpdateEventResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.ShipmentUpdate;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.processor.ShipmentEventProcessor;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogContext;
import com.cvshealth.digital.omni.library.oms.logging.util.OMSLogDiagnostics;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

public class ShipmentInboundBrokerServiceTest {


    private ShipmentInboundBrokerServiceValidator shipmentInboundBrokerServiceValidator;
    private ShipmentInboundBrokerService shipmentInboundBrokerService;
    private ShipmentEventProcessor shipmentEventProcessor;
    private EventPublisherService eventPublisherService;
    private ObjectMapper objectMapper;


    @BeforeEach
    void setUp() {
        List<ShipmentEventProcessor> shipmentEventProcessors = new ArrayList<>();
        shipmentEventProcessor = mock(ShipmentEventProcessor.class);
        shipmentEventProcessors.add(shipmentEventProcessor);
        eventPublisherService = mock(EventPublisherService.class);
        objectMapper = mock(ObjectMapper.class);
        MeterRegistry meterRegistry = new SimpleMeterRegistry();
        shipmentInboundBrokerServiceValidator = mock(ShipmentInboundBrokerServiceValidator.class);
        shipmentInboundBrokerService = new ShipmentInboundBrokerService(shipmentInboundBrokerServiceValidator, shipmentEventProcessors, eventPublisherService, objectMapper, meterRegistry);
    }

    @Test
    void processShipmentUpdateRequest_successfulProcessing() throws ApiException, JsonProcessingException {

        ShipmentUpdateEventRequest request = mock(ShipmentUpdateEventRequest.class);
        ShipmentUpdate shipmentUpdate = mock(ShipmentUpdate.class);

        when(request.getSourceSystemType()).thenReturn("VALID_SOURCE");
        when(request.getMessage()).thenReturn("testMessage");
        when(shipmentEventProcessor.canProcess("VALID_SOURCE")).thenReturn(true);
        when(shipmentEventProcessor.processShipmentEvent("testMessage")).thenReturn(shipmentUpdate);
        when(objectMapper.writeValueAsString(shipmentUpdate)).thenReturn("shipmentServiceRequest");

        ShipmentUpdateEventResponse response = shipmentInboundBrokerService.processShipmentUpdateRequest(request);

        verify(eventPublisherService, times(1)).publishEvent(eq("shipmentUpdateEvent"), eq("shipmentServiceRequest"), any());
        verify(shipmentEventProcessor, times(1)).canProcess("VALID_SOURCE");
        verify(shipmentEventProcessor, times(1)).sendUpdates("testMessage");
        verify(objectMapper, times(1)).writeValueAsString(shipmentUpdate);

        assertEquals("SUCCESS", response.getStatus());
    }

    @Test
    void processShipmentUpdateRequest_noProcessorFound() throws ApiException {

        ShipmentUpdateEventRequest request = mock(ShipmentUpdateEventRequest.class);

        when(request.getSourceSystemType()).thenReturn("INVALID_SOURCE");
        when(shipmentEventProcessor.canProcess("VALID_SOURCE")).thenReturn(true);

        ShipmentUpdateEventResponse response = shipmentInboundBrokerService.processShipmentUpdateRequest(request);

        verify(eventPublisherService, never()).publishEvent(any(), any(), any());
        assertEquals("FAILED", response.getStatus());
    }

    @Test
    void processShipmentUpdateRequest_sendUpdatesThrowsException() throws ApiException {

        ShipmentUpdateEventRequest request = mock(ShipmentUpdateEventRequest.class);
        ShipmentUpdate shipmentUpdate = mock(ShipmentUpdate.class);

        when(request.getSourceSystemType()).thenReturn("VALID_SOURCE");
        when(request.getMessage()).thenReturn("testMessage");
        when(shipmentEventProcessor.canProcess("VALID_SOURCE")).thenReturn(true);
        when(shipmentEventProcessor.processShipmentEvent("testMessage")).thenReturn(shipmentUpdate);
        doThrow(new RuntimeException("Test exception")).when(shipmentEventProcessor).sendUpdates("testMessage");

        ShipmentUpdateEventResponse response = shipmentInboundBrokerService.processShipmentUpdateRequest(request);

        verify(eventPublisherService, times(1)).publishEvent(eq("shipmentUpdateEvent"), any(), any());
        assertEquals("FAILED", response.getStatus());
    }

    @Test
    void processShipmentUpdateRequest_publishEventThrowsException() throws ApiException, JsonProcessingException {

        ShipmentUpdateEventRequest request = mock(ShipmentUpdateEventRequest.class);
        ShipmentUpdate shipmentUpdate = mock(ShipmentUpdate.class);

        when(request.getSourceSystemType()).thenReturn("VALID_SOURCE");
        when(request.getMessage()).thenReturn("testMessage");
        when(shipmentEventProcessor.canProcess("VALID_SOURCE")).thenReturn(true);
        when(shipmentEventProcessor.processShipmentEvent("testMessage")).thenReturn(shipmentUpdate);
        when(objectMapper.writeValueAsString(shipmentUpdate)).thenReturn("shipmentServiceRequest");
        doThrow(new RuntimeException("Test exception")).when(eventPublisherService).publishEvent(eq("shipmentUpdateEvent"), eq("shipmentServiceRequest"), any());

        ShipmentUpdateEventResponse response = shipmentInboundBrokerService.processShipmentUpdateRequest(request);

        verify(eventPublisherService, times(1)).publishEvent(eq("shipmentUpdateEvent"), eq("shipmentServiceRequest"), any());
        verify(shipmentEventProcessor, times(1)).sendUpdates("testMessage");

        assertEquals("FAILED", response.getStatus());
    }

    @Test
    void processShipmentUpdateRequest_validationFails() throws ApiException {

        ShipmentUpdateEventRequest request = mock(ShipmentUpdateEventRequest.class);

        doThrow(new ApiRequiredFieldMissingException(ApiErrors.buildRequiredFieldError("sourceSystemType"))).when(shipmentInboundBrokerServiceValidator).validate(request);

        assertThrows(ApiException.class, () -> {
            shipmentInboundBrokerService.processShipmentUpdateRequest(request);
        });

        verify(eventPublisherService, never()).publishEvent(any(), any(), any());
    }
}
