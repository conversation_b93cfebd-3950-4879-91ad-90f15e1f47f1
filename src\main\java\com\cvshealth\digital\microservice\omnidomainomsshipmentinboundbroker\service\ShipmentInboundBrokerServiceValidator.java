package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service;

import com.cvshealth.digital.framework.starter.exception.ApiErrors;
import com.cvshealth.digital.framework.starter.exception.api.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Service validator.
 *
 * <AUTHOR>
 */
@Component
public class ShipmentInboundBrokerServiceValidator {

    public ShipmentUpdateEventRequest validate(ShipmentUpdateEventRequest request) throws ApiException {

        if (null == request) {
            throw new ApiBadRequestException(ApiErrors.INVALID_REQUEST);
        }

        if (StringUtils.isBlank(request.getSourceSystemType())) {
            throw new ApiRequiredFieldMissingException(ApiErrors.buildRequiredFieldError("sourceSystemType"));
        }

        if (StringUtils.isBlank(request.getMessage())) {
            throw new ApiRequiredFieldMissingException(ApiErrors.buildRequiredFieldError("message"));
        }

        if ("{}".equalsIgnoreCase(request.getMessage())) {
            throw new ApiRequiredFieldMissingException(ApiErrors.buildRequiredFieldError("message"));
        }

        return request;
    }
}
