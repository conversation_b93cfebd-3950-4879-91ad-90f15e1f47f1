package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.mapper;

import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.PnpShipmentUpdateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.ShipmentUpdate;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.IOException;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertNotNull;

class PnpEventMapperTest {

    private PnpEventMapper pnpEventMapper;
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        pnpEventMapper = new PnpEventMapper();
        objectMapper = new ObjectMapper();
    }

    @Test
     void testMapWithJsonFiles() throws IOException {

        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath:json/*.json");

        System.out.println("nbr of JSON resources: " + resources.length);

        Arrays.stream(resources).forEach(resource -> {
            try {
                if(resource.getFilename().equalsIgnoreCase("PNP-RxHANDOFF.json")) {
                    PnpShipmentUpdateRequest request = objectMapper.readValue(resource.getInputStream(), PnpShipmentUpdateRequest.class);
                    ShipmentUpdate shipmentUpdate = pnpEventMapper.map(request);
                    assertNotNull(shipmentUpdate);
                    System.out.println("Processed file: " + resource.getFilename());
                    System.out.println(objectMapper.writeValueAsString(shipmentUpdate));
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }
}