package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.consumer;

import com.cvshealth.digital.framework.service.kafka.consumer.DigitalKafkaConsumerRecordProcessor;
import com.cvshealth.digital.framework.service.kafka.exception.KafkaConsumerException;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.ShipmentInboundBrokerService;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogContext;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogger;
import com.cvshealth.digital.omni.library.oms.logging.message.OMSLogMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Kafka consumer event processor. Implements <code>DigitalKafkaConsumerRecordProcessor</code>'s
 * <code>onMessage</code> to processes new messages.
 *
 * <AUTHOR> Brizard
 */
@RequiredArgsConstructor
@Service
public class ConsumerRecordProcessor implements DigitalKafkaConsumerRecordProcessor<String, String> {

    private static final int SUCCESS = 0;
    private static final int FAILED = -1;
    private final ShipmentInboundBrokerService shipmentInboundBrokerService;

    private static final OMSLogger logger = OMSLogger.getLogger(ConsumerRecordProcessor.class);

    @Override
    public Integer onMessage(String eventType, ConsumerRecord<String, String> consumerRecord) throws KafkaConsumerException {

        Map<String, Object> tags = new HashMap<>();
        tags.put("eventType", eventType);

        OMSLogMessage logMessage = OMSLogMessage.builder()
                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                .operationName("on-message")
                .serviceLayer("consumer")
                .message("Received message")
                .tags(tags).build();
        logger.entry(logMessage);

        OMSLogMessage omsLogMessage = OMSLogMessage.builder().tags(Map.of("Consumer Record Value",consumerRecord.value())).build();

        logger.debug(omsLogMessage);

        String sourceSystemType = null;
        Header sourceSystemTypeHeader = consumerRecord.headers().lastHeader("sourceSystemType");
        if (sourceSystemTypeHeader != null && sourceSystemTypeHeader.value() != null) {
            sourceSystemType = new String(sourceSystemTypeHeader.value());
        }

        ShipmentUpdateEventRequest request = new ShipmentUpdateEventRequest();
        request.setSourceSystemType(sourceSystemType);
        request.setMessage(consumerRecord.value());

        try {
            OMSLogContext.addTags("request",request);
            logger.debug("Calling service to process request");
            ShipmentUpdateEventResponse response = shipmentInboundBrokerService.processShipmentUpdateRequest(request);
            OMSLogMessage msg1 = OMSLogMessage.builder().message("Got response from service").build();
            OMSLogContext.addTags("response",response);
            logger.debug(msg1);
            return "SUCCESS".equalsIgnoreCase(response.getStatus()) ? SUCCESS : FAILED;
        } catch (ApiException e) {
            OMSLogContext.addTags("eventType",eventType);
            OMSLogContext.addTags("sourceSystemType",sourceSystemType);
            OMSLogContext.addTags("stacktrace",e);
            logger.error("Caught exception while processing request");
            return FAILED;
        }
        finally{
            OMSLogMessage msg = OMSLogMessage.builder().message("exited onMessage").build();
            logger.exit(msg);
        }

    }
}
