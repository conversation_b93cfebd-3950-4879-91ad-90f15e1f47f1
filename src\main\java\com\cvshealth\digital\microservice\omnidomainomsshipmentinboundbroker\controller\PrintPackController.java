package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.controller;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.mapper.PrintPackEventMapper;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.printpack.FetchShipmentPRNFileResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.printpack.PrintPackEventRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.AsyncKafkaPublisherService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.GCSService;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogger;
import com.cvshealth.digital.omni.library.oms.logging.message.OMSLogMessage;
import com.google.cloud.storage.StorageException;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.KafkaException;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
@Profile("specialty")
@RequiredArgsConstructor
@RestController
@RequestMapping("${service.context-path}")
public class PrintPackController {

    private final GCSService gcsService;
    private static final OMSLogger logger = OMSLogger.getLogger(ShipmentInboundBrokerController.class);
    private final PrintPackEventMapper printPackEventMapper;
    private final AsyncKafkaPublisherService asyncKafkaPublisherService;

    @GetMapping(value = "/fetch-shipment/prn", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<FetchShipmentPRNFileResponse> fetchShipmentPRNFile(@RequestBody PrintPackEventRequest request) throws ApiException {
        OMSLogMessage logMessage = OMSLogMessage.builder()
                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                .operationName("fetchShipmentPRNFile")
                .serviceLayer("controller")
                .message("Fetching PRN file from GCS")
                .tags(Map.of("request", request)).build();
        logger.debug(logMessage);
        FetchShipmentPRNFileResponse response;
        try {
            String encodedContent = gcsService.getEncodedContent(request.getFilePath());
            if (encodedContent == null) {
                 response = new FetchShipmentPRNFileResponse(request.getFilePath(), null, "FAILURE");
                return ResponseEntity.status(404).body(response);
            }
            response = new FetchShipmentPRNFileResponse(request.getFilePath(), encodedContent, "SUCCESS");

            Map<String, String> printPackHeaders = printPackEventMapper.printPackInput(request);

            logger.debug("Kafka Headers: {}" + printPackHeaders);
            Map<String, Object> emptyPayload = Map.of();

            try {
                asyncKafkaPublisherService.publishEventAsync("shipmentStatusUpdateEvent", emptyPayload, printPackHeaders);
            } catch (KafkaException ex) {
                logger.error("Kafka error while publishing event" + ex);
            } catch (Exception ex) {
                logger.error("Unexpected error while publishing event to Kafka", ex);
            }
            return ResponseEntity.ok(response);
        } catch (StorageException se) {
            if (se.getCode() == 404) {
                logger.error("GCS file not found for filePath: {}" + request.getFilePath(), se);
                response = new FetchShipmentPRNFileResponse(request.getFilePath(), null, "FAILURE");
                return ResponseEntity.status(404).body(response);
            }
            logger.error("GCS error while fetching PRN file for filePath: {}" + request.getFilePath(), se);
            response = new FetchShipmentPRNFileResponse(request.getFilePath(), null, "FAILURE");
            return ResponseEntity.status(500).body(response);
        } catch (Exception e) {
            logger.error("Failed to fetch PRN file from GCS for filePath: {}" + request.getFilePath(), e);
            response = new FetchShipmentPRNFileResponse(request.getFilePath(), null, "FAILURE");
            return ResponseEntity.status(500).body(response);
        }
    }
}