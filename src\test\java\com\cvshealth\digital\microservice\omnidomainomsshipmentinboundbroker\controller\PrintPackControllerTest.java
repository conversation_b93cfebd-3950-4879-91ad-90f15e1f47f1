package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.controller;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.printpack.FetchShipmentPRNFileResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.printpack.PrintPackEventRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.AsyncKafkaPublisherService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.GCSService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.mapper.PrintPackEventMapper;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.util.Base64Util;
import com.google.cloud.storage.StorageException;
import org.apache.kafka.common.KafkaException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.ResponseEntity;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

public class PrintPackControllerTest {

    private GCSService gcsService;
    private PrintPackEventMapper printPackEventMapper;
    private AsyncKafkaPublisherService asyncKafkaPublisherService;
    private PrintPackController controller;

    @BeforeEach
    public void setUp() {
        gcsService = Mockito.mock(GCSService.class);
        printPackEventMapper = Mockito.mock(PrintPackEventMapper.class);
        asyncKafkaPublisherService = Mockito.mock(AsyncKafkaPublisherService.class);
        controller = new PrintPackController(gcsService, printPackEventMapper, asyncKafkaPublisherService);
    }

    @Test
    public void testFetchShipmentPRNFile_success() throws Exception {
        PrintPackEventRequest request = new PrintPackEventRequest();
        request.setFilePath("test/path/file.txt");

        String encodedContent = Base64Util.encode("file-content");
        Mockito.when(gcsService.getEncodedContent(anyString())).thenReturn(encodedContent);
        Mockito.when(printPackEventMapper.printPackInput(any())).thenReturn(Map.of("header", "value"));

        ResponseEntity<FetchShipmentPRNFileResponse> response = controller.fetchShipmentPRNFile(request);

        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("SUCCESS", response.getBody().getStatus());
        assertEquals("test/path/file.txt", response.getBody().getFilePath());
        assertEquals(encodedContent, response.getBody().getContent());
        Mockito.verify(asyncKafkaPublisherService).publishEventAsync(anyString(), anyMap(), anyMap());
    }

    @Test
    public void testFetchShipmentPRNFile_gcsStorageException() throws Exception {
        PrintPackEventRequest request = new PrintPackEventRequest();
        request.setFilePath("bad/path/file.txt");

        Mockito.when(gcsService.getEncodedContent(anyString())).thenThrow(new StorageException(500, "GCS error"));

        ResponseEntity<FetchShipmentPRNFileResponse> response = controller.fetchShipmentPRNFile(request);

        assertEquals(500, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("FAILURE", response.getBody().getStatus());
        assertNull(response.getBody().getContent());
    }

    @Test
    public void testFetchShipmentPRNFile_kafkaException() throws Exception {
        PrintPackEventRequest request = new PrintPackEventRequest();
        request.setFilePath("test/path/file.txt");

        String encodedContent = Base64Util.encode("file-content");
        Mockito.when(gcsService.getEncodedContent(anyString())).thenReturn(encodedContent);
        Mockito.when(printPackEventMapper.printPackInput(any())).thenReturn(Map.of("header", "value"));
        Mockito.doThrow(new KafkaException("Kafka error")).when(asyncKafkaPublisherService).publishEventAsync(anyString(), anyMap(), anyMap());

        ResponseEntity<FetchShipmentPRNFileResponse> response = controller.fetchShipmentPRNFile(request);

        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("SUCCESS", response.getBody().getStatus());
    }

    @Test
    public void testFetchShipmentPRNFile_genericException() throws Exception {
        PrintPackEventRequest request = new PrintPackEventRequest();
        request.setFilePath("any/path/file.txt");

        Mockito.when(gcsService.getEncodedContent(anyString())).thenThrow(new RuntimeException("Unexpected error"));

        ResponseEntity<FetchShipmentPRNFileResponse> response = controller.fetchShipmentPRNFile(request);

        assertEquals(500, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("FAILURE", response.getBody().getStatus());
        assertNull(response.getBody().getContent());
    }

    @Test
    public void testFetchShipmentPRNFile_nullEncodedContent() throws Exception {
        PrintPackEventRequest request = new PrintPackEventRequest();
        request.setFilePath("test/path/file.txt");

        Mockito.when(gcsService.getEncodedContent(anyString())).thenReturn(null);
        Mockito.when(printPackEventMapper.printPackInput(any())).thenReturn(Map.of("header", "value"));

        ResponseEntity<FetchShipmentPRNFileResponse> response = controller.fetchShipmentPRNFile(request);

        assertEquals(404, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("FAILURE", response.getBody().getStatus());
        assertNull(response.getBody().getContent());
    }

    @Test
    public void testFetchShipmentPRNFile_printPackEventMapperException() throws Exception {
        PrintPackEventRequest request = new PrintPackEventRequest();
        request.setFilePath("test/path/file.txt");

        String encodedContent = Base64Util.encode("file-content");
        Mockito.when(gcsService.getEncodedContent(anyString())).thenReturn(encodedContent);
        Mockito.when(printPackEventMapper.printPackInput(any())).thenThrow(new RuntimeException("Mapper error"));

        ResponseEntity<FetchShipmentPRNFileResponse> response = controller.fetchShipmentPRNFile(request);

        assertEquals(500, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("FAILURE", response.getBody().getStatus());
        assertNull(response.getBody().getContent());
    }
}

