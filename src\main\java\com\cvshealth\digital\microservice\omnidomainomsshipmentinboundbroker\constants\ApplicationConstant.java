package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.constants;

public final class ApplicationConstant {

    private ApplicationConstant() {
    }

    public static final String PREP = "PREP";
    public static final String EMPTY = "";
    public static final Integer INT_ZERO = 0;
    public static final String STR_DOUBLE_ZERO = "0.00";
    public static final String STR_INT_ZERO = "0";
    public static final String VALUE_YES = "Y";
    public static final String VALUE_NO = "N";
    public static final String TAG_SUBMIT_TO_OMS_SUCCESS = "submit_to_oms_success";
    public static final String TAG_ERROR_SEVERITY = "error-severity";
    public static final String ERROR_SEVERITY_CRITICAL = "critical";
    public static final String TAG_DMA_RESPONSE_STATUS = "dma_response_status";
    public static final String YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";

    // these headers are used for RxDelivery
    public static final String HEADER_SOURCE = "source";
    public static final String HEADER_ACTION = "action";
    public static final String HEADER_LINE_OF_BUSINESS = "lineOfBusiness";
    public static final String HEADER_ORDER_TYPE = "orderType";
    public static final String HEADER_ORDER_KEY = "orderKey";
    public static final String HEADER_TIMESTAMP = "timestamp";
    public static final String HEADER_CONVERSATION_ID = "conversationId";
}
