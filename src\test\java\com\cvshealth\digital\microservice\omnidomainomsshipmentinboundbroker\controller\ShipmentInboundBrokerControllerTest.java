package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.controller;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.PnpShipmentUpdateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.GCSService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Map;

public class ShipmentInboundBrokerControllerTest {

    private ShipmentInboundBrokerController controller = null;
    private EventPublisherService mockEventPublisherService;
    private ObjectMapper mockObjectMapper;


    @BeforeEach
    public void setUp() {
        mockEventPublisherService = Mockito.mock(EventPublisherService.class);
        mockObjectMapper = Mockito.mock(ObjectMapper.class);
        controller = new ShipmentInboundBrokerController(mockEventPublisherService,mockObjectMapper);

    }

    @Test
    public void testPNPUpdateEndpoint_success() throws ApiException, JsonProcessingException {

        PnpShipmentUpdateRequest request = new PnpShipmentUpdateRequest();
        request.setRequestMetaData(null);
        request.setRequestPayloadData(null);

        Mockito.when(mockObjectMapper.writeValueAsString(request)).thenReturn("requestString");
        Mockito.when(mockEventPublisherService.publishEvent("shipmentUpdateEvent", "requestString", Map.of("sourceSystemType", "PNP"))).thenReturn(new ApiResponse());

        ApiResponse response = controller.updatePNPShipmentStatus(request);

        Assertions.assertNotNull(response);

        Mockito.verify(mockObjectMapper, Mockito.times(1)).writeValueAsString(request);
        Mockito.verify(mockEventPublisherService, Mockito.times(1)).publishEvent("shipmentUpdateEvent", "requestString", Map.of("sourceSystemType", "PNP"));
    }
}