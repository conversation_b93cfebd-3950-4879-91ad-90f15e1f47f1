package functional.com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker;

import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.OmniDomainOmsShipmentInboundBrokerApplication;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.PnpShipmentUpdateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.ShipmentUpdate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.restassured.RestAssured;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.RecordedRequest;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.test.context.ActiveProfiles;

import static io.restassured.RestAssured.given;
import static org.junit.jupiter.api.Assertions.*;

import okhttp3.mockwebserver.MockWebServer;
import org.springframework.test.context.DynamicPropertySource;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@SpringBootTest(classes = OmniDomainOmsShipmentInboundBrokerApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Slf4j
@Disabled("Disabled until we can run in GHA CI pipeline")
public class PublishPNPUpdateEventTest {

    @LocalServerPort
    private int port;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private DefaultKafkaConsumerFactory<String, String> kafkaConsumerFactory;

    private static MockWebServer mockWebServer;
    private static String baseUrl;


    @BeforeAll
    public static void setup() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
        baseUrl = mockWebServer.url("/").toString();
    }

    @BeforeEach
    public void setUp() {
        RestAssured.port = port;
        RestAssured.basePath = "/microservices/omni-domain-oms-shipment-inbound-broker";
        RestAssured.filters(new RequestLoggingFilter(), new ResponseLoggingFilter());
    }

    @AfterEach
    public void tearDown() throws InterruptedException {
        int requestCount = mockWebServer.getRequestCount();

        if (requestCount > 0) {
            log.info("MockWebServer received {} requests. Clearing queue..", requestCount);
            RecordedRequest recordedRequest;
            do {
                recordedRequest = mockWebServer.takeRequest(3000, java.util.concurrent.TimeUnit.MILLISECONDS);
            } while ((recordedRequest != null));
        } else {
            log.info("No requests were made to MockWebServer.");
        }

    }

    @DynamicPropertySource
    static void properties(org.springframework.test.context.DynamicPropertyRegistry registry) {
        registry.add("clients.dma.url", () -> baseUrl + "/delivery/publish/v1/pnp/event");
    }

    @Test
    public void testUpdatePNPShipmentStatus_whenValidRequest() throws IOException, InterruptedException {

        // Given
        PnpShipmentUpdateRequest pnpShipmentUpdateRequest = getPnpShipmentUpdateRequest();
        mockWebCalls();

        // When
        Response response = given()
            .contentType("application/json")
            .body(pnpShipmentUpdateRequest)
                .when()
                .post("/update-shipment-status/pnp")
                .thenReturn();

        // Then
        if (response.getStatusCode() != 200) {
            throw new RuntimeException("Failed to update PNP shipment status: " + response.getBody().asString());
        }

        ApiResponse apiResponse = response.as(ApiResponse.class);
        assertNotNull(response);
        assertEquals("Success", apiResponse.getStatusDescription());
        assertEquals("0000", apiResponse.getStatusCode());

        Thread.sleep(10000); // give time for consumer to get message

        shouldSeeDMACallMade(pnpShipmentUpdateRequest);
        shouldHaveMessageInUpdateTopic(pnpShipmentUpdateRequest, true);
    }

    @Test
    public void testUpdatePNPShipmentStatus_whenInvalidRequest_emptyJSON() throws InterruptedException {

        // Given
        PnpShipmentUpdateRequest pnpShipmentUpdateRequest = new PnpShipmentUpdateRequest();

        mockWebCalls();

        // When
        Response response = given()
                .contentType("application/json")
                .body(pnpShipmentUpdateRequest)
                .when()
                .post("/update-shipment-status/pnp")
                .thenReturn();

        // Then
        if (response.getStatusCode() != 200) {
            throw new RuntimeException("Failed to update PNP shipment status: " + response.getBody().asString());
        }

        ApiResponse apiResponse = response.as(ApiResponse.class);
        assertNotNull(response);
        assertEquals("Success", apiResponse.getStatusDescription());
        assertEquals("0000", apiResponse.getStatusCode());

        Thread.sleep(10000); // give time for consumer to get message

        shouldNotSeeDMACallMade(pnpShipmentUpdateRequest);
        shouldHaveMessageInUpdateTopic(pnpShipmentUpdateRequest, false);
    }

    private void mockWebCalls() {

        mockWebServer.setDispatcher(new Dispatcher() {
            @NotNull
            @Override
            public MockResponse dispatch(@NotNull RecordedRequest recordedRequest) {
                if (recordedRequest.getRequestUrl() != null
                        && recordedRequest.getRequestUrl().toString().contains("/delivery/publish/v1/pnp/event")) {
                    return new MockResponse().setResponseCode(200);
                }
                return new MockResponse().setResponseCode(404);
            }
        });

    }

    private PnpShipmentUpdateRequest getPnpShipmentUpdateRequest() throws IOException {

        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath:json/*.json");

        Optional<Resource> foundFile = Arrays.stream(resources).filter(resource -> Objects.equals(resource.getFilename(), "PNP-RxHANDOFF.json")).findFirst();

        if (foundFile.isEmpty()) {
            throw new RuntimeException("PNP-RxHANDOFF.json file not found in classpath");
        }

        PnpShipmentUpdateRequest request = objectMapper.readValue(foundFile.get().getInputStream(), PnpShipmentUpdateRequest.class);
        request.getRequestPayloadData().getAdditionalData().setOrderID(UUID.randomUUID().toString());

        return request;
    }

    private void shouldSeeDMACallMade(PnpShipmentUpdateRequest pnpShipmentUpdateRequest) throws InterruptedException {

        RecordedRequest recordedRequest = mockWebServer.takeRequest(3000, TimeUnit.MILLISECONDS);

        assertNotNull(recordedRequest);
        assertNotNull(recordedRequest.getRequestUrl());
        assertTrue(recordedRequest.getRequestUrl().toString().contains("/delivery/publish/v1/pnp/event"));
        assertTrue(recordedRequest.getBody().readUtf8().contains(pnpShipmentUpdateRequest.getRequestPayloadData().getAdditionalData().getOrderID()));
    }

    private void shouldNotSeeDMACallMade(PnpShipmentUpdateRequest pnpShipmentUpdateRequest) throws InterruptedException {

        RecordedRequest recordedRequest = mockWebServer.takeRequest(3000, TimeUnit.MILLISECONDS);

        assertNull(recordedRequest);
    }

    private void shouldHaveMessageInUpdateTopic(PnpShipmentUpdateRequest pnpShipmentUpdateRequest, boolean shouldReceiveMessage) {

        Map<String, Object> factoryConfigProps = kafkaConsumerFactory.getConfigurationProperties();

        Properties configProps = new Properties();
        configProps.putAll(factoryConfigProps);
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:29092");

        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, "omni-domain-oms-shipment-inbound-broker-test");
        configProps.put(ConsumerConfig.CLIENT_ID_CONFIG, "omni-domain-oms-shipment-inbound-broker-test-client");
        configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "10");
        configProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, "10000");
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        configProps.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "5000");
        configProps.put("security.protocol", "SASL_PLAINTEXT");
        configProps.put("ssl.endpoint.identification.algorithm", "");
        configProps.put("sasl.mechanism", "PLAIN");
        configProps.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"admin\" password=\"admin123\";");

        TopicPartition topicPartition = new TopicPartition("digitalomni-oms-specialty-shipment-status-update-event-test", 0);
        Consumer<String, String> consumer = kafkaConsumerFactory.createConsumer("omni-domain-oms-shipment-inbound-broker", "functional", "test", configProps);
        consumer.assign(List.of(topicPartition));
        consumer.seek(topicPartition, 0);

        ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(5000));

        AtomicBoolean foundMessage = new AtomicBoolean(false);

        do {
            records.forEach(record -> {
                log.info("Received message: {}", record.value());
                try {
                    ShipmentUpdate msg = objectMapper.readValue(record.value(), ShipmentUpdate.class);
                    if (pnpShipmentUpdateRequest.getRequestPayloadData() != null && pnpShipmentUpdateRequest.getRequestPayloadData().getAdditionalData() != null) {
                        String orderId = pnpShipmentUpdateRequest.getRequestPayloadData().getAdditionalData().getOrderID();
                        if (msg.getMessageType().equalsIgnoreCase("HANDOFF") && msg.getDeliveryCode().equalsIgnoreCase("MC3ODD")
                                && msg.getShipmentNo().equalsIgnoreCase(orderId)) {
                            log.info("Found message for orderId: {}", orderId);
                            foundMessage.set(true);
                        }
                    }
                } catch (JsonProcessingException e) {
                    log.error("Caught JsonProcessingException while reading message: {}", record.value(), e);
                }
            });
            records = consumer.poll(Duration.ofMillis(5000));
        } while (records.count() > 0);

        consumer.commitSync();

        if (shouldReceiveMessage) {
            assertTrue(foundMessage.get());
        } else {
            assertFalse(foundMessage.get());
        }

    }
}
