package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DispositionData {
    private String priceSign;
    private String priceSource;
    private String mfTxnRecordCd;
    private String mfTxnTypeInd;
    private String mfTxnTypeCd;
    private String mfPollDt;
    private String mfOriginalTxn;
    private String mfSku;
    private String registerTxnSeqNum;
    private String saleTymStmp;
    private String registerNum;
    private String txnNum;
    private String txnType;
    private String userId;
    private List<RxList> rxList;
}
