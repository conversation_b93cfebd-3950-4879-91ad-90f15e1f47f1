package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.config;

import java.util.HashMap;
import java.util.Map;

/**
 * Application properties. Add application specific properties here.
 * It is configured in <code>AppConfig</code> with prefix: app
 *
 * <AUTHOR>
 */
@lombok.Data
public class ApplicationProperties {

    /**
     * Kafka application event properties.
     */
    public Map<String, EventConfig> events = new HashMap<>();

    @lombok.Data
    public static class EventConfig {
        /**
         * For application code to turn feature on/off. Default: true
         */
        private boolean enabled = true;
        /**
         * Topic name to publish events.
         */
        private String topic = "";
        // other event properties
    }

}
