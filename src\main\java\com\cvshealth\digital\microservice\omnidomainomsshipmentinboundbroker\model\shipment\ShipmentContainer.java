package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShipmentContainer {
    private String trackingNo;
    private String carrierServiceCode;
    private String scac;
    private String containerHeight;
    private String containerWidth;
    private String containerLength;
    private String containerGrossWeight;
    private List<ContainerItem> containerItemList;
}
