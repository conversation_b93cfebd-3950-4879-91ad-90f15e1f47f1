package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.specialty340b;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class Specialty340bRx {
    private Long shipmentItemId;
    private String rxNumber;
    private String itemId;
    private Double quantity;
    private String packByUserId;
    private String packByUserName;
    private String packDt;
    private ArrayList<Specialty340bContainer> containers = new ArrayList<>();
}
