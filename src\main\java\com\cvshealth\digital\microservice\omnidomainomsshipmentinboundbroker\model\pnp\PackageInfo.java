package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PackageInfo {
    private String trackingNo;
    private String shipperName;
    private String serviceCode;
    private String packageHeight;
    private String packageWeight;
    private String packageLength;
    private String packageWidth;
    private List<PackageItemList> itemList;
}