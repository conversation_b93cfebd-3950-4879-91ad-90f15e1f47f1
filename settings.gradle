pluginManagement {
    plugins {

    }
    repositories {
        gradlePluginPortal()
        maven {
            name = "jfrog"
            url = uri("https://cvsh.jfrog.io/artifactory/cvsdigital-maven")
            credentials {
                username System.getenv("ARTIFACTORY_USERNAME")
                password System.getenv("ARTIFACTORY_TOKEN")
            }
        }
    }
}
plugins {
    id 'org.gradle.toolchains.foojay-resolver-convention' version '0.5.0'
}

/*
 * This file was generated by the Gradle 'init' task.
 *
 * The settings file is used to specify which projects to include in your build.
 * For more detailed information on multi-project builds, please refer to https://docs.gradle.org/8.7/userguide/multi_project_builds.html in the Gradle documentation.
 */

rootProject.name = 'omni-domain-oms-shipment-inbound-broker'
