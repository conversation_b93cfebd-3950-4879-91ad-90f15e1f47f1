package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdditionalData {
    private String orderID;
    private String storeNumber;
    private String deliveryMode;
    private String associateID;
    private String associateFirstName;
    private String transactionId;
    private String transactionDateTime;
    private String posRegisterNumber;
    private String numberOfPackages;
    private String orderPromisedPickUpTime;
    private String eventTimestamp;
    private String extraCareCardNo;
    private List<Item> itemList;
    private List<PackageInfo> packageInfo;
    private DispositionData dispositionData;
    private String cancelReasonCode;
    private String cancelReasonDesc;
    private String bagCount;
    private String boxCount;
    private String errorCode;
    private String errorDesc;
    private String shippingCharges;
    private String interfaceType;
    private String orderType;
    private String paymentPreference;
    private String orderPromisedDeliveryTime;
    private String lockerTerminalID;
    private String pin;
    private String barcode;
    private List<String> orderLabelBarcodeList;
}