package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.util;

import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;

import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.dma.DMAResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.oms.DeliveryInputMessage;
import io.github.resilience4j.retry.annotation.Retry;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import static org.springframework.http.MediaType.APPLICATION_JSON;

@Component
public class CVSRestApiClient {

    @Value("${clients.dma.url}")
    String dmaURL;

    private final RestClient restClient;

    public CVSRestApiClient(RestClient restClient) {
        this.restClient = restClient;
    }

    @Retry(name="dma-service")

    public DMAResponse invokeDMAApi(DeliveryInputMessage deliveryInputMessage) {

        LogServiceContext.addTags("dmaURL", dmaURL);

        return restClient.post()
                .uri(dmaURL)
                .contentType(APPLICATION_JSON)
                .body(deliveryInputMessage)
                .retrieve()
                .onStatus(HttpStatusCode::isError, (request, response) -> {
                    throw  new RuntimeException(response.getStatusText()); // TODO: Create a custom exception
                })
                .body(DMAResponse.class);
    }

}