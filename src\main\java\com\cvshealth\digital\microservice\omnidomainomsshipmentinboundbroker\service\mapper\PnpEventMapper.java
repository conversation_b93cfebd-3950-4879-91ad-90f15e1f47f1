package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.mapper;

import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.cvs.oms.common.util.ResourceHelper.resolve;
import static com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.constants.ApplicationConstant.*;
import static com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.util.AppUtil.removeLeadingZero;

/**
 * <AUTHOR>
 */
@Component
public class PnpEventMapper {

    public ShipmentUpdate map(PnpShipmentUpdateRequest pnpShipmentUpdateRequest) {
        AdditionalData additionalData = pnpShipmentUpdateRequest.getRequestPayloadData().getAdditionalData();
        ShipmentUpdate shipmentUpdate = mapShipmentUpdate(additionalData, pnpShipmentUpdateRequest.getRequestPayloadData().getData().getEventName());
        mapDispositionData(additionalData, shipmentUpdate);
        mapShipmentLines(shipmentUpdate, additionalData);
        mapContainer(shipmentUpdate, additionalData);
        return shipmentUpdate;
    }

    public ShipmentUpdate mapShipmentUpdate(AdditionalData additionalData, String eventName) {
        ShipmentUpdate shipmentUpdate = new ShipmentUpdate();
        shipmentUpdate.setShipNode(removeLeadingZero(additionalData.getStoreNumber()));
        shipmentUpdate.setShipmentNo(resolve(additionalData::getOrderID).orElse(EMPTY));
        shipmentUpdate.setMessageType(eventName);
        shipmentUpdate.setDeliveryCode(resolve(additionalData::getDeliveryMode).orElse(EMPTY));
        shipmentUpdate.setOrderType(resolve(additionalData::getOrderType).orElse(EMPTY));
        shipmentUpdate.setAssociateFirstName(resolve(additionalData::getAssociateFirstName).orElse(EMPTY));
        shipmentUpdate.setAssociateId(resolve(additionalData::getAssociateID).orElse(EMPTY));
        shipmentUpdate.setPosTransactionNo(resolve(additionalData::getTransactionId).orElse(EMPTY));
        shipmentUpdate.setPosTransactionDate(resolve(() -> additionalData.getDispositionData().getSaleTymStmp()).orElse(EMPTY));
        shipmentUpdate.setCancelledReason(resolve(additionalData::getCancelReasonDesc).orElse(EMPTY));
        shipmentUpdate.setCancelReasonCode(resolve(additionalData::getCancelReasonCode).orElse(EMPTY));
        int boxCount = StringUtils.hasText(additionalData.getBoxCount()) ? Integer.parseInt(additionalData.getBagCount()) : INT_ZERO;
        shipmentUpdate.setNumOfBoxes(boxCount);
        int bagCount = StringUtils.hasText(additionalData.getBagCount()) ? Integer.parseInt(additionalData.getBagCount()) : INT_ZERO;
        shipmentUpdate.setNumOfBags(bagCount);
        List<Barcode> barcodeList = new ArrayList<>();
        resolve(additionalData::getOrderLabelBarcodeList).ifPresent(lstBarcodes -> lstBarcodes.forEach(barcodeValue -> {
            Barcode barcode = new Barcode();
            barcode.setBarcodeValue(barcodeValue);
            barcodeList.add(barcode);
        }));
        shipmentUpdate.setBarcodeList(barcodeList);
        return shipmentUpdate;
    }

    private void mapDispositionData(AdditionalData additionalData, ShipmentUpdate shipmentUpdate) {
        ShipmentUpdateDispositionData shipmentUpdateDispositionData = new ShipmentUpdateDispositionData();
        if (!Objects.isNull(additionalData.getDispositionData())) {
            shipmentUpdateDispositionData.setMfOriginalTxn(resolve(() -> additionalData.getDispositionData().getMfOriginalTxn()).orElse(EMPTY));
            shipmentUpdateDispositionData.setMfPollDt(resolve(() -> additionalData.getDispositionData().getMfPollDt()).orElse(EMPTY));
            shipmentUpdateDispositionData.setPosSaleTimeStamp(resolve(() -> additionalData.getDispositionData().getSaleTymStmp()).orElse(EMPTY));
            shipmentUpdateDispositionData.setMfTxnRecordCd(resolve(() -> additionalData.getDispositionData().getMfTxnRecordCd()).orElse(EMPTY));
            shipmentUpdateDispositionData.setMfTxnTypeCd(resolve(() -> additionalData.getDispositionData().getMfTxnTypeCd()).orElse(EMPTY));
            shipmentUpdateDispositionData.setMfTxnTypeInd(resolve(() -> additionalData.getDispositionData().getMfTxnTypeInd()).orElse(EMPTY));
            shipmentUpdateDispositionData.setMfSku(resolve(() -> additionalData.getDispositionData().getMfSku()).orElse(EMPTY));
            shipmentUpdateDispositionData.setRegisterNum(resolve(() -> additionalData.getDispositionData().getRegisterNum()).orElse(EMPTY));
            shipmentUpdateDispositionData.setRegisterTxnSeqNum(resolve(() -> additionalData.getDispositionData().getRegisterTxnSeqNum()).orElse(EMPTY));
            shipmentUpdateDispositionData.setPriceSign(resolve(() -> additionalData.getDispositionData().getPriceSign()).orElse(EMPTY));
            shipmentUpdateDispositionData.setPriceSource(resolve(() -> additionalData.getDispositionData().getPriceSource()).orElse(EMPTY));
            shipmentUpdateDispositionData.setTxnNo(resolve(() -> additionalData.getDispositionData().getTxnNum()).orElse(EMPTY));
            shipmentUpdateDispositionData.setTxnType(resolve(() -> additionalData.getDispositionData().getTxnType()).orElse(EMPTY));
            shipmentUpdateDispositionData.setUserId(resolve(() -> additionalData.getDispositionData().getUserId()).orElse(EMPTY));
        }
        shipmentUpdate.setShipmentUpdateDispositionData(shipmentUpdateDispositionData);
    }

    private void mapContainer(ShipmentUpdate shipmentUpdate, AdditionalData additionalData) {
        List<PackageInfo> packageInfoList = additionalData.getPackageInfo();
        if (!CollectionUtils.isEmpty(packageInfoList)) {
            List<ShipmentContainer> shipmentContainerList = new ArrayList<>();
            packageInfoList.forEach(packageInfo -> {
                ShipmentContainer shipmentContainer = new ShipmentContainer();
                shipmentContainer.setTrackingNo(resolve(packageInfo::getTrackingNo).orElse(EMPTY));
                shipmentContainer.setCarrierServiceCode(resolve(packageInfo::getServiceCode).orElse(EMPTY));
                shipmentContainer.setScac(resolve(packageInfo::getShipperName).orElse(EMPTY));
                shipmentContainer.setContainerHeight(resolve(packageInfo::getPackageHeight).orElse(EMPTY));
                shipmentContainer.setContainerWidth(resolve(packageInfo::getPackageWidth).orElse(EMPTY));
                shipmentContainer.setContainerLength(resolve(packageInfo::getPackageLength).orElse(EMPTY));
                shipmentContainer.setContainerGrossWeight(resolve(packageInfo::getPackageWeight).orElse(EMPTY));
                if (!CollectionUtils.isEmpty(packageInfo.getItemList())) {
                    List<ContainerItem> containerItemList = new ArrayList<>();
                    packageInfo.getItemList().forEach(packageItem -> {
                        ContainerItem containerItem = new ContainerItem();
                        containerItem.setItemId(resolve(packageItem::getItemId).orElse(EMPTY));
                        containerItem.setOrderLineNo(Integer.parseInt(resolve(packageItem::getPrimeLineNumber).orElse("1")));
                        containerItem.setSubLineNo(1);
                        containerItem.setQty(resolve(() -> Integer.parseInt(packageItem.getQuantity())).orElse(INT_ZERO));
                        containerItemList.add(containerItem);
                    });
                    shipmentContainer.setContainerItemList(containerItemList);
                }
                shipmentContainerList.add(shipmentContainer);
            });
            shipmentUpdate.setShipmentContainerList(shipmentContainerList);
        }
    }

    protected void mapShipmentLines(ShipmentUpdate shipmentUpdate, AdditionalData additionalData) {
        List<Item> itemList = additionalData.getItemList();
        List<ShipmentItem> shipmentItemList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemList)) {
            itemList.forEach(item -> {
                ShipmentItem shipmentItem = new ShipmentItem();
                shipmentItem.setOrderedQuantity(Integer.parseInt(resolve(item::getOrderedQty).orElse(STR_INT_ZERO)));
                shipmentItem.setPackedQty(Integer.parseInt(resolve(item::getPickedQty).orElse(STR_INT_ZERO)));
                shipmentItem.setLineCancellationReason(resolve(() -> removeSpecialCharacters(item.getCancelledReasonDesc())).orElse(EMPTY));
                shipmentItem.setCancelledDate(resolve(item::getCancelledDate).orElse(EMPTY));
                shipmentItem.setCancelledQty(resolve(() -> Integer.parseInt(getCancelledQty(item.getPickedQty(), item.getOrderedQty()))).orElse(INT_ZERO));
                shipmentItem.setOrderLineNo(Integer.parseInt(resolve(item::getPrimeLineNumber).orElse(STR_INT_ZERO)));
                shipmentItem.setSubLineNo(1);
                shipmentItem.setItemId(resolve(item::getItemId).orElse(EMPTY));
                shipmentItem.setUpcSubstituteItem(resolve(item::getUpcSubstituteItem).orElse(EMPTY));
                shipmentItem.setOrderedQuantity(Integer.parseInt(resolve(item::getOrderedQty).orElse(STR_INT_ZERO)));
                shipmentItem.setTax(Double.valueOf(resolve(item::getTax).orElse(STR_DOUBLE_ZERO)));
                shipmentItem.setSubstitutedItemId(resolve(item::getSubstitutedItemId).orElse(EMPTY));
                shipmentItem.setStoreUnitPrice(Double.valueOf(resolve(item::getStoreUnitPrice).orElse(STR_DOUBLE_ZERO)));
                shipmentItem.setUnitPrice(Double.valueOf(resolve(item::getUnitPrice).orElse(STR_DOUBLE_ZERO)));
                shipmentItemList.add(shipmentItem);
            });
        }
        List<RxList> rxList = getRxlist(additionalData);
        if (!CollectionUtils.isEmpty(rxList)) {
            rxList.forEach(rxItem -> {
                ShipmentItem shipmentItem = new ShipmentItem();
                shipmentItem.setRxNumber(rxItem.getRxNum());
                shipmentItem.setFillNumber(rxItem.getRefillNum());
                shipmentItem.setFillSeqNum(String.valueOf(rxItem.getPartialFillSeqNum()));
                shipmentItem.setFillVerNum(String.valueOf(rxItem.getFillVersionNumber()));
                shipmentItemList.add(shipmentItem);
            });
        }
        shipmentUpdate.setShipmentItemList(shipmentItemList);
    }

    public String removeSpecialCharacters(String sString) {
        // Check if the input string is neither null nor empty
        if (Objects.nonNull(sString) && StringUtils.hasText(sString)) {
            // Replace special characters based on the regex pattern
            return sString.replaceAll("[^A-Za-z0-9' ():._]", "");
        }
        // Return an empty string if the input is null or empty
        return "";
    }

    private String getCancelledQty(String pickedQty, String orderedQty) {
        return (StringUtils.hasText(pickedQty) && StringUtils.hasText(orderedQty)) ?
                String.valueOf(Integer.parseInt(orderedQty) - Integer.parseInt(pickedQty)) : orderedQty;
    }

    public String posTransactionDate(AdditionalData additionalData) {
        if (Objects.isNull(additionalData) || Objects.isNull(additionalData.getDispositionData())) {
            return null;
        }
        return additionalData.getDispositionData().getSaleTymStmp();
    }

    private List<RxList> getRxlist(AdditionalData additionalData) {
        if (Objects.isNull(additionalData) || Objects.isNull(additionalData.getDispositionData())
                || CollectionUtils.isEmpty(additionalData.getDispositionData().getRxList())) {
            return null;
        }
        return additionalData.getDispositionData().getRxList()
                .stream()
                .filter(rxItem -> StringUtils.hasText(rxItem.getRxNum()))
                .collect(Collectors.toList());
    }

}
