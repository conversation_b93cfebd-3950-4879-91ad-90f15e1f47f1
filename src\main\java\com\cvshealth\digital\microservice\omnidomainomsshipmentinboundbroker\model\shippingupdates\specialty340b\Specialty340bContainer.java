package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.specialty340b;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.util.ArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class Specialty340bContainer {
    private String containerId;
    private String checkByUserId;
    private String checkDt;
    private String pickByUserId;
    private String pickDt;
    private Double dispenseQuantityNb;
    private ArrayList<Specialty340bContainerContents> containerContents = new ArrayList<>();
}
