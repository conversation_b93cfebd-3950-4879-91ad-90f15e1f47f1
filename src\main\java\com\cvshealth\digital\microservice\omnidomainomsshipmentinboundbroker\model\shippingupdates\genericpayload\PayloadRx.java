package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.genericpayload;

import lombok.Data;

import java.util.ArrayList;

@Data
public class PayloadRx {
    private Long shipmentItemId;
    private String rxNumber;
    private String itemId;
    private Double quantity;
    private String packByUserId;
    private String packByUserName;
    private String packDt;
    private ArrayList<PayloadContainer> containers = new ArrayList<>();
}
