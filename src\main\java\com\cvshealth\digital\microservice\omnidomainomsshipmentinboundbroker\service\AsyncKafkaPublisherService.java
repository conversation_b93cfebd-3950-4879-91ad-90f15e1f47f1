package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service;

import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.controller.ShipmentInboundBrokerController;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogger;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.Map;
@Service
@EnableAsync
@Configuration

public class AsyncKafkaPublisherService {
    private final EventPublisherService eventPublisherService;
    private static final OMSLogger logger
            = OMSLogger.getLogger(ShipmentInboundBrokerController.class);
    public AsyncKafkaPublisherService(EventPublisherService eventPublisherService) {
        this.eventPublisherService = eventPublisherService;
    }

    @Async
    public void publishEventAsync(String eventType, Map<String, Object> payload, Map<String, String> headers) {
        try {
            //logger.info("Publishing event Headers: " + headers);
            eventPublisherService.publishEvent(eventType, payload.toString(), headers);

        } catch (ApiException e) {
            logger.error("Unable to publish event to Kafka", e);
        }
    }
}