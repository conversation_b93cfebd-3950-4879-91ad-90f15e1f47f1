package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer;

import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.config.ApplicationProperties;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.config.ApplicationProperties.EventConfig;
import com.cvshealth.digital.framework.service.kafka.producer.DigitalKafkaProducer;
import com.cvshealth.digital.framework.starter.exception.ApiErrors;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.exception.api.ApiRequiredFieldMissingException;
import com.cvshealth.digital.framework.starter.exception.model.ServiceStatusCodes;
import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogger;
import com.cvshealth.digital.omni.library.oms.logging.message.OMSLogMessage;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Kafka event publisher service.
 *
 * <AUTHOR> Brizard
 */
@RequiredArgsConstructor
@Service
public class EventPublisherService {

    private final DigitalKafkaProducer<String, String> digitalKafkaProducer;
    private final ApplicationProperties applicationProperties;
    private static final OMSLogger logger = OMSLogger.getLogger(EventPublisherService.class);

    public ApiResponse publishEvent(String eventType, String message, Map<String, String> headers) throws ApiException {

        EventConfig eventConfig = applicationProperties.getEvents().get(eventType);

        if (eventConfig == null) {
            throw new ApiRequiredFieldMissingException(ApiErrors.buildRequiredFieldError("eventConfig"));
        }

        logger.debug(OMSLogMessage.builder()
                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                .operationName("publish-event")
                .serviceLayer("producer")
                .message("Found event config")
                .tags(Map.of("eventType",eventType, "eventConfig",eventConfig)).build());

        if (StringUtils.isBlank(eventConfig.getTopic())) {
            throw new ApiRequiredFieldMissingException(ApiErrors.buildRequiredFieldError("topic"));
        }

        logger.debug(OMSLogMessage.builder()
                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                .operationName("publish-event")
                .serviceLayer("producer")
                .message("Publishing event")
                .tags(Map.of("eventType",eventType)).build());

        // send message, throws KafkaClientException on exception
        digitalKafkaProducer.sendMessage(eventConfig.getTopic(), null, message, headers);

        logger.debug(OMSLogMessage.builder()
                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                .operationName("publish-event")
                .serviceLayer("producer")
                .message("Published event to topic: " + eventConfig.getTopic() + " with message: " + message)
                .tags(Map.of("eventType",eventType)).build());

        // on success
        return new ApiResponse(ServiceStatusCodes.STATUS_CODE_0000_SUCCESS, ServiceStatusCodes.SUCCESS);
    }
}
