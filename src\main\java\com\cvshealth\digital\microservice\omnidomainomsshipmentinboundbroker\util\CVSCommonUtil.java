package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.util;

import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.constants.ApplicationConstant;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;


public class CVSCommonUtil {

    private CVSCommonUtil() {
    }

    public static String currentDareTime(){
        DateTimeFormatter format = DateTimeFormatter.ofPattern(ApplicationConstant.YYYY_MM_DD_T_HH_MM_SS);
        return format.format(LocalDateTime.now());
    }

    public static String generateTypeUUID() throws NoSuchAlgorithmException {
        try {

            String standardChars ="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

            byte[] bytes = standardChars.getBytes(StandardCharsets.UTF_8);
            MessageDigest md = MessageDigest.getInstance("SHA-1");

            byte[] hash = md.digest(bytes);

            long msb = getLeastAndMostSignificantBitsVersion5(hash, 0);
            long lsb = getLeastAndMostSignificantBitsVersion5(hash, 8);
            msb &= ~(0xfL << 12);
            msb |= 5L << 12;
            lsb &= ~(0x3L << 62);
            lsb |= 2L << 62;
            return new UUID(msb, lsb).toString();

        } catch (NoSuchAlgorithmException e) {
            throw new AssertionError(e);
        }
    }

    private static long getLeastAndMostSignificantBitsVersion5(final byte[] src, final int offset) {
        long ans = 0;
        for (int i = offset + 7; i >= offset; i -= 1) {
            ans <<= 8;
            ans |= src[i] & 0xffL;
        }
        return ans;
    }

}
