package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shippingupdates.genericpayload;

import lombok.Data;

import java.util.ArrayList;

@Data
public class PayloadContainer {
    private String containerId;
    private String checkByUserId;
    private String checkDt;
    private String pickByUserId;
    private String pickDt;
    private Double dispenseQuantityNb;
    private ArrayList<PayloadContainerContents> containerContents = new ArrayList<>();
}
