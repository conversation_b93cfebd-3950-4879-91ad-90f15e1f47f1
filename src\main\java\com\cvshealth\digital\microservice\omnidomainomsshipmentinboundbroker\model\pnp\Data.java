package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@lombok.Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Data{
    private String authType;
    private String eventName;
    private String channelID;
    private String channelType;
    private String memberID;
    private String memberType;
    private String deviceID;
    private String deviceType;
    private String clientVersion;
    private String timeZone;
    private String ipAddress;
    private String isRepack;
    private List<String> destinationType;
    private List<String> destination;
}