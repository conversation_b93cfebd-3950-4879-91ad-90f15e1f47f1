package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RxList {
    private String rxNum;
    private String refillNum;
    private String unitPrice;
    private int partialFillSeqNum;
    private int fillVersionNumber;
}
