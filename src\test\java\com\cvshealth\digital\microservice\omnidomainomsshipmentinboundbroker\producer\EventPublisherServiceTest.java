package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer;

import com.cvshealth.digital.framework.starter.exception.model.ServiceStatusCodes;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.config.ApplicationProperties;
import com.cvshealth.digital.framework.service.kafka.producer.DigitalKafkaProducer;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.exception.api.ApiRequiredFieldMissingException;
import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

class EventPublisherServiceTest {

    @Mock
    private DigitalKafkaProducer<String, String> digitalKafkaProducer;

    @Mock
    private ApplicationProperties applicationProperties;

    @Mock
    private ApplicationProperties.EventConfig eventConfig;

    @InjectMocks
    private EventPublisherService eventPublisherService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void publishEvent_success() throws ApiException {
        String eventType = "testEvent";
        String request = "testRequest";
        Map<String, String> headers = new HashMap<>();
        headers.put("headerKey", "headerValue");

        Map<String, ApplicationProperties.EventConfig> events = Map.of(eventType, eventConfig);
        when(applicationProperties.getEvents()).thenReturn(events);
        when(eventConfig.getTopic()).thenReturn("testTopic");

        ApiResponse response = eventPublisherService.publishEvent(eventType, request, headers);

        verify(digitalKafkaProducer, times(1)).sendMessage("testTopic", null, request, headers);
        assertEquals(ServiceStatusCodes.STATUS_CODE_0000_SUCCESS, response.getStatusCode());
    }

    @Test
    void publishEvent_eventConfigNotFound() {
        String eventType = "invalidEvent";
        String request = "testRequest";
        Map<String, String> headers = new HashMap<>();

        Map<String, ApplicationProperties.EventConfig> events = Map.of(eventType+"1", eventConfig);
        when(applicationProperties.getEvents()).thenReturn(events);

        assertThrows(ApiRequiredFieldMissingException.class, () -> {
            eventPublisherService.publishEvent(eventType, request, headers);
        });
    }

    @Test
    void publishEvent_topicIsBlank() {
        String eventType = "testEvent";
        String request = "testRequest";
        Map<String, String> headers = new HashMap<>();

        Map<String, ApplicationProperties.EventConfig> events = Map.of(eventType, eventConfig);
        when(applicationProperties.getEvents()).thenReturn(events);
        when(eventConfig.getTopic()).thenReturn("");

        assertThrows(ApiRequiredFieldMissingException.class, () -> {
            eventPublisherService.publishEvent(eventType, request, headers);
        });
    }
}