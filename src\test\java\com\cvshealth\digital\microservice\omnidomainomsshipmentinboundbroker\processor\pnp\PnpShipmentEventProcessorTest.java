package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.processor.pnp;

import com.cvshealth.digital.framework.starter.exception.api.ApiBadRequestException;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.exception.api.ApiServiceException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.constants.EventType;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.Data;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.PnpShipmentUpdateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.RequestMetaData;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.RequestPayloadData;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.ShipmentUpdate;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.mapper.PnpEventMapper;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.processor.pnp.PnpShipmentEventProcessor;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogContext;
import com.cvshealth.digital.omni.library.oms.logging.util.OMSLogDiagnostics;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

class PnpShipmentEventProcessorTest {

    @Mock
    private PnpEventMapper pnpEventMapper;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private PnpShipmentEventProcessor pnpShipmentEventProcessor;



    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void processShipmentEvent_success() throws ApiException, JsonProcessingException {
        String message = "testMessage";
        PnpShipmentUpdateRequest request = new PnpShipmentUpdateRequest();
        RequestMetaData meta = new RequestMetaData();
        request.setRequestMetaData(meta);
        RequestPayloadData payload = new RequestPayloadData();
        Data data = new Data();
        data.setEventName(EventType.CANCEL.name());
        payload.setData(data);
        request.setRequestPayloadData(payload);
        ShipmentUpdate shipmentUpdate = mock(ShipmentUpdate.class);

        when(objectMapper.readValue(message, PnpShipmentUpdateRequest.class)).thenReturn(request);
        when(pnpEventMapper.map(request)).thenReturn(shipmentUpdate);

        ShipmentUpdate result = pnpShipmentEventProcessor.processShipmentEvent(message);

        assertEquals(shipmentUpdate, result);
    }

    @Test
    void processShipmentEvent_invalidEventType() throws JsonProcessingException {
        String message = "testMessage";
        PnpShipmentUpdateRequest request = new PnpShipmentUpdateRequest();
        RequestMetaData meta = new RequestMetaData();
        request.setRequestMetaData(meta);
        RequestPayloadData payload = new RequestPayloadData();
        Data data = new Data();
        data.setEventName(EventType.CANCEL.name() + "1");
        payload.setData(data);
        request.setRequestPayloadData(payload);

        when(objectMapper.readValue(message, PnpShipmentUpdateRequest.class)).thenReturn(request);

        assertThrows(ApiBadRequestException.class, () -> {
            pnpShipmentEventProcessor.processShipmentEvent(message);
        });
    }

    @Test
    void processShipmentEvent_jsonProcessingException() throws JsonProcessingException {
        String message = "testMessage";

        when(objectMapper.readValue(message, PnpShipmentUpdateRequest.class)).thenThrow(new JsonProcessingException("Test exception") {});

        assertThrows(ApiServiceException.class, () -> {
            pnpShipmentEventProcessor.processShipmentEvent(message);
        });
    }

    @Test
    void canProcess_validSourceSystem() {
        boolean result = pnpShipmentEventProcessor.canProcess("PNP");
        assertEquals(true, result);
    }

    @Test
    void canProcess_invalidSourceSystem() {
        boolean result = pnpShipmentEventProcessor.canProcess("INVALID");
        assertEquals(false, result);
    }
}