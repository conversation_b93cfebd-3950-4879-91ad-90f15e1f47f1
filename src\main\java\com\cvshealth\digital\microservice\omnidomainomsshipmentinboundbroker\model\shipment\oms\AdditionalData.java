package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.oms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalData{
    private String orderID;
    private String storeNumber;
    private String deliveryMode;
    private String deliveryCode;
    private String eventTimestamp;
    private String interfaceType;
}
