package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShipmentUpdate {

    private String messageType;  // PREP, HANDOFF, CANCEL
    private String shipNode;
    private String deliveryCode; // "SFS_Mobile", "BOPUS_Mobile", "MC3ODD", etc.
    private String shipmentNo;
    private String orderNo;
    private String shipmentType;
    private String deliveryMethod;
    private String orderType;
    private String shipmentCancellationReason;
    private int numOfBags;
    private int numOfBoxes;
    private String rxLockerPin;
    private String carrierServiceName;
    private String associateFirstName;
    private String associateId;
    private String posTransactionNo;
    private String posTransactionDate;
    private String cancelledDate;
    private String cancelReasonCode;
    private String cancelledReason;
    private ShipmentUpdateDispositionData shipmentUpdateDispositionData;
    private List<Barcode> barcodeList;
    private List<ShipmentItem> shipmentItemList;
    private List<ShipmentContainer> shipmentContainerList;
}