package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.consumer;

import com.cvshealth.digital.framework.service.kafka.consumer.DigitalKafkaConsumerRecordProcessor;
import com.cvshealth.digital.framework.service.kafka.exception.KafkaConsumerException;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogger;
import com.cvshealth.digital.omni.library.oms.logging.message.OMSLogMessage;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Service;

import java.lang.invoke.MethodHandles;
import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
@Service
public class AckNackEventProcessor implements DigitalKafkaConsumerRecordProcessor<String, String> {

    private static final int SUCCESS = 0;
    private static final int FAILED = -1;
    private static final OMSLogger logger = OMSLogger.getLogger(MethodHandles.lookup().lookupClass());

    private final EventPublisherService eventPublisherService;

    @Override
    public Integer onMessage(String eventType, ConsumerRecord<String, String> consumerRecord) throws KafkaConsumerException {

        OMSLogMessage.OMSLogMessageBuilder logMessage = OMSLogMessage.builder()
                .appName("OMS-SHIPMENT-INBOUND-BROKER")
                .operationName("ack-nack-on-message")
                .serviceLayer("consumer")
                .message("Received message")
                .tags(Map.of("eventType", eventType));
        logger.entry(logMessage.build());

        try {
            HashMap<String, String> headers = new HashMap<>();
            consumerRecord.headers().forEach((header) -> {
                headers.put(header.key(), new String(header.value()));
            });
            eventPublisherService.publishEvent("shipmentStatusUpdateEvent", consumerRecord.value(), headers);

            return SUCCESS;
        } catch (ApiException e) {
            logger.error(logMessage.message("Caught exception while processing request").build(), e);
            return FAILED;
        }
    }
}
