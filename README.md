# omni-domain-oms-shipment-inbound-broker

The OMS Shipment Inbound Broker is a service used to process status events from clients.

# Local Development

## Building
Open Terminal and run the following command:
```shell
gradle clean build
```
Or inside IntelliJ, click the Build (hammer) icon and then click "Sync Gradle Project".

**Note**: If building from the command line, make sure you have Gradle 8.4 or higher or else you will get errors.  This project has been tested using Gradle 8.7 and Gradle 8.8 (either works).


## Docker Compose
To run the application locally, you can use Docker Compose.  Open Terminal and run the following command:
```shell
docker-compose up -d
```

## Create Topic
Now create the topic in Kafka. Can do the following in IntelliJ:

Open the Kafka tool window: View | Tool Windows | Kafka.

Now right-click on Topics and select "Create Topic".  Create two topics with the IDs: 
* `digitalomni-oms-specialty-shipment-inbound-broker-event`
* `digitalomni-oms-specialty-shipment-status-inbound-broker-event`


## Running
From inside IntelliJ, right-click on the `OmniDomainOmsShipmentInboundBrokerApplication` class and select `Run`.  Make sure that you edit the settings to use the "local" profile.

## Testing
Now you can place a message on the topic. Can do the following in IntelliJ:

Open the Kafka tool window: View | Tool Windows | Kafka.

Now right-click on the topic you created above and select "Create Kafka Producer".  Enter the following values:

```json
{
  "key": "12345",
  "value": "SHIPPED"
}
```

In the logs you should see the message is being processed.

# Documentation

## Swagger
http://localhost:8080/microservices/omni-domain-oms-shipment-inbound-broker/swagger/swagger-ui/index.html

## Confluence
https://cvsdigital.atlassian.net/wiki/spaces/EDFS/pages/**********/OMS+Shipment+Inbound+Broker+Service


# CI/CD Links

## GitHub Actions
https://github.com/cvs-health-source-code/omni-domain-oms-shipment-inbound-broker/actions

## ArgoCD
TBD

# Monitoring

## Splunk
TBD

## Grafana
TBD