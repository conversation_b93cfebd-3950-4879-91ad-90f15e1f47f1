package com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.processor.pnp;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;
import com.cvshealth.digital.framework.starter.exception.ApiErrors;
import com.cvshealth.digital.framework.starter.exception.api.ApiBadRequestException;
import com.cvshealth.digital.framework.starter.exception.api.ApiException;
import com.cvshealth.digital.framework.starter.exception.api.ApiServiceException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.constants.ApplicationConstant;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.constants.EventType;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.pnp.PnpShipmentUpdateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.ShipmentUpdate;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.dma.DMAResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.model.shipment.oms.DeliveryInputMessage;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.mapper.PnpEventMapper;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.service.processor.ShipmentEventProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentinboundbroker.util.CVSRestApiClient;
import com.cvshealth.digital.omni.library.oms.logging.OMSLogger;
import com.cvshealth.digital.omni.library.oms.logging.message.OMSLogMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR> Brizard
 * <AUTHOR> Mittal
 */

@Component
@AllArgsConstructor
public class PnpShipmentEventProcessor implements ShipmentEventProcessor {

    private PnpEventMapper pnpEventMapper;
    private ObjectMapper objectMapper;
    private final CVSRestApiClient cvsRestApiClient;
    private static final OMSLogger logger = OMSLogger.getLogger(PnpShipmentEventProcessor.class);


    @Override
    public ShipmentUpdate processShipmentEvent(String message) throws ApiException {

        OMSLogMessage entryLogMessage= OMSLogMessage.builder().message("Processing PNP message: processShipmentEvent")
                .tags(Map.of("message",message)).build();
        logger.entry(entryLogMessage);

        // since this processor is being called we should be dealing with a PnpShipmentUpdateRequest message
        PnpShipmentUpdateRequest pnpShipmentUpdateRequest;

        try {
            pnpShipmentUpdateRequest = objectMapper.readValue(message, PnpShipmentUpdateRequest.class);
        } catch (JsonProcessingException e) {
            OMSLogMessage errorLogMessage= OMSLogMessage.builder()
                    .message("Caught JsonProcessingException while reading PNP message")
                    .tags(Map.of("message",message,"stacktrace",e)).build();
            logger.error(errorLogMessage);
            throw new ApiServiceException(e);
        }
        finally {
            OMSLogMessage msg= OMSLogMessage.builder().message("exited processShipmentEvent").build();
            logger.exit(msg);
        }

        if (isEventApplicable(pnpShipmentUpdateRequest.getRequestPayloadData().getData().getEventName())) {
            return pnpEventMapper.map(pnpShipmentUpdateRequest);
        } else {
            OMSLogMessage errorLogMessage= OMSLogMessage.builder().message("Invalid event type")
                    .tags(Map.of("event-name", pnpShipmentUpdateRequest.getRequestPayloadData().getData().getEventName()
                            ,"converstation-id", pnpShipmentUpdateRequest.getRequestMetaData().getConversationID() != null ? pnpShipmentUpdateRequest.getRequestMetaData().getConversationID() : ""))
                    .build();
            logger.error(errorLogMessage);
            String errorMessage = String.format("%s: is invalid event type", pnpShipmentUpdateRequest.getRequestPayloadData().getData().getEventName());
            throw new ApiBadRequestException(ApiErrors.buildRequiredFieldError(errorMessage));
        }

    }

    @Override
    public void sendUpdates(String message) throws ApiException {

        logger.debug("Sending updates to DMA for PNP message: " + message);

        // 3-18-2025 - based on conversation with Yashu, for now we can call dma endpoint to post the update so that
        // flow remains as is. there is some audit DMA is maintaining of all the pnp updates received. later we can see
        // to port that logic in our inbound broker and then just post directly to oms topic.

        try {
            DeliveryInputMessage deliveryInputMessage = objectMapper.readValue(message, DeliveryInputMessage.class);

            OMSLogMessage sendingUpdateMessage= OMSLogMessage.builder().message("Sending update message to DMA using deliveryInputMessage")
                    .tags(Map.of("delivery-input-message",deliveryInputMessage))
                    .build();

            logger.info(sendingUpdateMessage);

            DMAResponse response = cvsRestApiClient.invokeDMAApi(deliveryInputMessage);

            OMSLogMessage receivedUpdateMessage= OMSLogMessage.builder().message("Received update response from DMA")
                    .tags(Map.of("response",response))
                    .build();

            logger.info(receivedUpdateMessage);

            if (response != null && response.getStatus() != null && !response.getStatus().isEmpty()) {
                LogServiceContext.addTags(ApplicationConstant.TAG_DMA_RESPONSE_STATUS, response.getStatus());
            }

            LogServiceContext.addTags(ApplicationConstant.TAG_SUBMIT_TO_OMS_SUCCESS, ApplicationConstant.VALUE_YES);

        } catch (Exception e) {
            LogServiceContext.addTags(ApplicationConstant.TAG_ERROR_SEVERITY, ApplicationConstant.ERROR_SEVERITY_CRITICAL);
            LogServiceContext.addTags(ApplicationConstant.TAG_SUBMIT_TO_OMS_SUCCESS, ApplicationConstant.VALUE_NO);
            CvsLogger.error("Error sending update to DMA", e);
        }
    }

    private boolean isEventApplicable(String eventName) {
        return Arrays.stream(EventType.values()).anyMatch(eventType -> eventType.name().equalsIgnoreCase(eventName));
    }

    @Override
    public boolean canProcess(String eventSourceSystem) {
        return "PNP".equalsIgnoreCase(eventSourceSystem);
    }
}
